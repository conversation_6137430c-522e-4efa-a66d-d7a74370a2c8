from django.urls import path
from . import views

app_name = 'manufacturing'

urlpatterns = [
    # لوحة التحكم
    path('', views.manufacturing_dashboard, name='dashboard'),
    
    # أوامر التصنيع
    path('orders/', views.manufacturing_order_list, name='order_list'),
    path('orders/create/', views.manufacturing_order_create, name='order_create'),
    path('orders/<int:order_id>/', views.manufacturing_order_detail, name='order_detail'),
    path('orders/<int:order_id>/edit/', views.manufacturing_order_edit, name='order_edit'),
    path('orders/<int:order_id>/approve/', views.manufacturing_order_approve, name='order_approve'),
    path('orders/<int:order_id>/start-production/', views.manufacturing_order_start_production, name='order_start_production'),
    path('orders/<int:order_id>/complete/', views.manufacturing_order_complete, name='order_complete'),
    path('orders/<int:order_id>/print/', views.manufacturing_order_print, name='order_print'),

    # API endpoints
    path('api/product-cost/<int:product_id>/', views.get_product_cost_api, name='product_cost_api'),
    path('api/product-unit/<int:product_id>/', views.get_product_unit_api, name='product_unit_api'),

    # صفحة اختبار API
    path('test-api/', views.test_api_view, name='test_api'),
]
