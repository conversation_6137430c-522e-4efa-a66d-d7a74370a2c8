{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    
    .manufacturing-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        position: relative;
        overflow: hidden;
    }
    
    .manufacturing-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }
    
    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }
    
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .status-badge {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-size: 1rem;
        font-weight: 700;
        text-align: center;
        display: inline-block;
        min-width: 120px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    
    .status-draft { 
        background: linear-gradient(135deg, #e9ecef, #dee2e6); 
        color: #495057; 
    }
    .status-approved { 
        background: linear-gradient(135deg, #d1ecf1, #bee5eb); 
        color: #0c5460; 
    }
    .status-in_progress { 
        background: linear-gradient(135deg, #fff3cd, #ffeaa7); 
        color: #856404; 
    }
    .status-completed { 
        background: linear-gradient(135deg, #d4edda, #c3e6cb); 
        color: #155724; 
    }
    .status-cancelled { 
        background: linear-gradient(135deg, #f8d7da, #f5c6cb); 
        color: #721c24; 
    }
    
    .priority-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
    }
    
    .priority-low { background: linear-gradient(135deg, #d1f2eb, #a3e4d7); color: #0e6655; }
    .priority-normal { background: linear-gradient(135deg, #d6eaf8, #aed6f1); color: #1b4f72; }
    .priority-high { background: linear-gradient(135deg, #fadbd8, #f1948a); color: #943126; }
    .priority-urgent { background: linear-gradient(135deg, #f1948a, #ec7063); color: #ffffff; }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .info-item {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 15px;
        padding: 1.5rem;
        border-left: 4px solid #667eea;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    }
    
    .info-label {
        font-size: 0.9rem;
        color: #636e72;
        font-weight: 600;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .info-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2d3436;
    }
    
    .cost-summary {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 20px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: center;
    }
    
    .cost-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
    }
    
    .cost-item {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .cost-label {
        font-size: 1rem;
        color: #1565c0;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .cost-value {
        font-size: 2rem;
        font-weight: 900;
        color: #0d47a1;
    }
    
    .materials-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin: 2rem 0;
    }
    
    .materials-table .table {
        margin: 0;
    }
    
    .materials-table .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1.5rem 1rem;
        font-weight: 700;
        text-align: center;
    }
    
    .materials-table .table tbody td {
        padding: 1.5rem 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .materials-table .table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }
    
    .availability-badge {
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
    }
    
    .available { background: #d4edda; color: #155724; }
    .unavailable { background: #f8d7da; color: #721c24; }
    .partial { background: #fff3cd; color: #856404; }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin: 2rem 0;
        flex-wrap: wrap;
    }
    
    .btn-action {
        border-radius: 15px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 700;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }
    
    .btn-primary-action {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }
    
    .btn-primary-action:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-success-action {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        color: white;
        box-shadow: 0 10px 25px rgba(86, 171, 47, 0.3);
    }
    
    .btn-success-action:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(86, 171, 47, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-warning-action {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        box-shadow: 0 10px 25px rgba(240, 147, 251, 0.3);
    }
    
    .btn-warning-action:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-info-action {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
        box-shadow: 0 10px 25px rgba(116, 185, 255, 0.3);
    }
    
    .btn-info-action:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(116, 185, 255, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .btn-secondary-action {
        background: linear-gradient(135deg, #636e72 0%, #495057 100%);
        color: white;
        box-shadow: 0 10px 25px rgba(99, 110, 114, 0.3);
    }
    
    .btn-secondary-action:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(99, 110, 114, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .progress-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 20px;
        padding: 2rem;
        margin: 2rem 0;
    }
    
    .progress-bar-custom {
        height: 20px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: all 0.3s ease;
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 800;
        color: #2d3436;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }
    
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .info-grid {
            grid-template-columns: 1fr;
        }
        
        .cost-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .btn-action {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="manufacturing-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    {{ page_title }}
                </h1>
                <div class="mt-3">
                    <span class="status-badge status-{{ order.status }}">
                        {{ order.get_status_display }}
                    </span>
                    <span class="priority-badge priority-{{ order.priority }} ms-3">
                        {{ order.get_priority_display }}
                    </span>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:order_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        {% if order.status == 'draft' %}
            <a href="{% url 'manufacturing:order_edit' order.id %}" class="btn-action btn-warning-action">
                <i class="bi bi-pencil"></i>تعديل الأمر
            </a>
            <a href="{% url 'manufacturing:order_approve' order.id %}" class="btn-action btn-success-action">
                <i class="bi bi-check-circle"></i>اعتماد الأمر
            </a>
        {% elif order.status == 'approved' %}
            <a href="{% url 'manufacturing:order_start_production' order.id %}" class="btn-action btn-primary-action">
                <i class="bi bi-play-circle"></i>بدء الإنتاج
            </a>
        {% elif order.status == 'in_progress' %}
            <a href="{% url 'manufacturing:order_complete' order.id %}" class="btn-action btn-success-action">
                <i class="bi bi-check-circle"></i>إكمال الإنتاج
            </a>
        {% endif %}

        <a href="{% url 'manufacturing:order_print' order.id %}" class="btn-action btn-info-action" target="_blank">
            <i class="bi bi-printer"></i>طباعة الأمر
        </a>

        <button type="button" class="btn-action btn-secondary-action" onclick="window.print()">
            <i class="bi bi-download"></i>تصدير PDF
        </button>
    </div>

    <!-- معلومات أساسية -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-info-circle"></i>
            </div>
            المعلومات الأساسية
        </h2>

        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">رقم أمر التصنيع</div>
                <div class="info-value">{{ order.order_number }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">تاريخ الأمر</div>
                <div class="info-value">{{ order.order_date|date:"d/m/Y H:i" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">تاريخ الإنجاز المتوقع</div>
                <div class="info-value">{{ order.expected_completion_date|date:"d/m/Y H:i" }}</div>
            </div>
            {% if order.actual_completion_date %}
            <div class="info-item">
                <div class="info-label">تاريخ الإنجاز الفعلي</div>
                <div class="info-value">{{ order.actual_completion_date|date:"d/m/Y H:i" }}</div>
            </div>
            {% endif %}
            <div class="info-item">
                <div class="info-label">أنشئ بواسطة</div>
                <div class="info-value">{{ order.created_by.get_full_name|default:order.created_by.username }}</div>
            </div>
            {% if order.approved_by %}
            <div class="info-item">
                <div class="info-label">معتمد بواسطة</div>
                <div class="info-value">{{ order.approved_by.get_full_name|default:order.approved_by.username }}</div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- معلومات المنتج -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-box"></i>
            </div>
            معلومات المنتج
        </h2>

        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">المنتج النهائي</div>
                <div class="info-value">{{ order.final_product.name }}</div>
                <small class="text-muted">{{ order.final_product.code }}</small>
            </div>
            <div class="info-item">
                <div class="info-label">الكمية المطلوب إنتاجها</div>
                <div class="info-value">{{ order.quantity_to_produce }} {{ order.unit_of_measure.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">مخزن المواد الخام</div>
                <div class="info-value">{{ order.raw_materials_warehouse.name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">مخزن المنتجات التامة</div>
                <div class="info-value">{{ order.finished_goods_warehouse.name }}</div>
            </div>
        </div>
    </div>

    <!-- تحليل التكاليف -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-currency-dollar"></i>
            </div>
            تحليل التكاليف
        </h2>

        <div class="cost-summary">
            <h4 class="mb-4">التكاليف المقدرة</h4>
            <div class="cost-grid">
                <div class="cost-item">
                    <div class="cost-label">تكلفة المواد الخام</div>
                    <div class="cost-value">{{ order.estimated_raw_material_cost|floatformat:2 }}</div>
                    <small>جنيه</small>
                </div>
                <div class="cost-item">
                    <div class="cost-label">تكلفة العمالة</div>
                    <div class="cost-value">{{ order.estimated_labor_cost|floatformat:2 }}</div>
                    <small>جنيه</small>
                </div>
                <div class="cost-item">
                    <div class="cost-label">التكاليف الإضافية</div>
                    <div class="cost-value">{{ order.estimated_overhead_cost|floatformat:2 }}</div>
                    <small>جنيه</small>
                </div>
                <div class="cost-item">
                    <div class="cost-label">إجمالي التكلفة المقدرة</div>
                    <div class="cost-value">{{ order.total_estimated_cost|floatformat:2 }}</div>
                    <small>جنيه</small>
                </div>
            </div>

            {% if order.status in 'in_progress,completed' %}
            <hr class="my-4">
            <h4 class="mb-4">التكاليف الفعلية</h4>
            <div class="cost-grid">
                <div class="cost-item">
                    <div class="cost-label">تكلفة المواد الخام الفعلية</div>
                    <div class="cost-value">{{ order.actual_raw_material_cost|floatformat:2 }}</div>
                    <small>جنيه</small>
                </div>
                <div class="cost-item">
                    <div class="cost-label">تكلفة العمالة الفعلية</div>
                    <div class="cost-value">{{ order.actual_labor_cost|floatformat:2 }}</div>
                    <small>جنيه</small>
                </div>
                <div class="cost-item">
                    <div class="cost-label">التكاليف الإضافية الفعلية</div>
                    <div class="cost-value">{{ order.actual_overhead_cost|floatformat:2 }}</div>
                    <small>جنيه</small>
                </div>
                <div class="cost-item">
                    <div class="cost-label">إجمالي التكلفة الفعلية</div>
                    <div class="cost-value">{{ order.total_actual_cost|floatformat:2 }}</div>
                    <small>جنيه</small>
                </div>
            </div>

            {% if order.cost_variance != 0 %}
            <div class="mt-4">
                <h5>انحراف التكلفة:
                    <span class="{% if order.cost_variance > 0 %}text-danger{% else %}text-success{% endif %}">
                        {{ order.cost_variance|floatformat:2 }} جنيه
                        ({{ order.cost_variance_percentage|floatformat:1 }}%)
                    </span>
                </h5>
            </div>
            {% endif %}
            {% endif %}
        </div>
    </div>

    <!-- المواد الخام -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-layers"></i>
            </div>
            المواد الخام المطلوبة
        </h2>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="info-item">
                    <div class="info-label">إجمالي المواد</div>
                    <div class="info-value">{{ raw_materials_stats.total_materials }}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-item">
                    <div class="info-label">مواد حرجة</div>
                    <div class="info-value">{{ raw_materials_stats.critical_materials }}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-item">
                    <div class="info-label">مواد متوفرة</div>
                    <div class="info-value">{{ raw_materials_stats.available_materials }}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-item">
                    <div class="info-label">إجمالي التكلفة</div>
                    <div class="info-value">{{ raw_materials_stats.total_cost|floatformat:2 }} جنيه</div>
                </div>
            </div>
        </div>

        <div class="materials-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>المادة الخام</th>
                        <th>الكمية المطلوبة</th>
                        <th>الكمية المستهلكة</th>
                        <th>وحدة القياس</th>
                        <th>تكلفة الوحدة</th>
                        <th>إجمالي التكلفة</th>
                        <th>حالة التوفر</th>
                        <th>خصائص</th>
                    </tr>
                </thead>
                <tbody>
                    {% for material in order.raw_materials.all %}
                    <tr>
                        <td>
                            <div class="fw-bold">{{ material.raw_material.name }}</div>
                            <small class="text-muted">{{ material.raw_material.code }}</small>
                        </td>
                        <td class="text-center">
                            <strong>{{ material.required_quantity }}</strong>
                        </td>
                        <td class="text-center">
                            <strong>{{ material.consumed_quantity }}</strong>
                            {% if material.consumed_quantity > 0 %}
                                <br><small class="text-success">({{ material.consumption_percentage|floatformat:1 }}%)</small>
                            {% endif %}
                        </td>
                        <td class="text-center">{{ material.unit_of_measure.name }}</td>
                        <td class="text-end">{{ material.unit_cost|floatformat:2 }} جنيه</td>
                        <td class="text-end">
                            <strong class="text-success">{{ material.total_cost|floatformat:2 }} جنيه</strong>
                        </td>
                        <td class="text-center">
                            {% if material.is_available %}
                                <span class="availability-badge available">متوفرة</span>
                            {% elif material.shortage_quantity > 0 %}
                                <span class="availability-badge partial">نقص: {{ material.shortage_quantity }}</span>
                            {% else %}
                                <span class="availability-badge unavailable">غير متوفرة</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if material.is_critical %}
                                <span class="badge bg-danger">حرجة</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="bi bi-inbox" style="font-size: 2rem; color: #6c757d;"></i>
                            <p class="mt-2 text-muted">لم يتم تحديد مواد خام لهذا الأمر</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- تقدم الإنتاج -->
    {% if order.status in 'in_progress,completed' %}
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-graph-up"></i>
            </div>
            تقدم الإنتاج
        </h2>

        <div class="progress-section">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>نسبة الإنجاز الإجمالية</h5>
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar progress-bar-custom" role="progressbar"
                             style="width: {{ order.progress_percentage }}%;"
                             aria-valuenow="{{ order.progress_percentage }}"
                             aria-valuemin="0" aria-valuemax="100">
                            {{ order.progress_percentage|floatformat:1 }}%
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-6">
                            <div class="info-item">
                                <div class="info-label">إجمالي المراحل</div>
                                <div class="info-value">{{ stages_stats.total_stages }}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="info-item">
                                <div class="info-label">مراحل مكتملة</div>
                                <div class="info-value">{{ stages_stats.completed_stages }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- معلومات إضافية -->
    {% if order.notes or order.special_instructions or order.quality_requirements %}
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-file-text"></i>
            </div>
            معلومات إضافية
        </h2>

        <div class="info-grid">
            {% if order.notes %}
            <div class="info-item">
                <div class="info-label">ملاحظات</div>
                <div class="info-value">{{ order.notes|linebreaks }}</div>
            </div>
            {% endif %}

            {% if order.special_instructions %}
            <div class="info-item">
                <div class="info-label">تعليمات خاصة</div>
                <div class="info-value">{{ order.special_instructions|linebreaks }}</div>
            </div>
            {% endif %}

            {% if order.quality_requirements %}
            <div class="info-item">
                <div class="info-label">متطلبات الجودة</div>
                <div class="info-value">{{ order.quality_requirements|linebreaks }}</div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- معلومات النظام -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-clock-history"></i>
            </div>
            معلومات النظام
        </h2>

        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">تاريخ الإنشاء</div>
                <div class="info-value">{{ order.created_at|date:"d/m/Y H:i" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">آخر تحديث</div>
                <div class="info-value">{{ order.updated_at|date:"d/m/Y H:i" }}</div>
            </div>
            {% if order.started_at %}
            <div class="info-item">
                <div class="info-label">تاريخ بدء الإنتاج</div>
                <div class="info-value">{{ order.started_at|date:"d/m/Y H:i" }}</div>
            </div>
            {% endif %}
            {% if order.is_overdue %}
            <div class="info-item">
                <div class="info-label">حالة التأخير</div>
                <div class="info-value text-danger">متأخر</div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
