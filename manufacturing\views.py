from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Sum, Count, Avg
from django.utils import timezone
from django.template.loader import render_to_string
from decimal import Decimal
import json

from .models import (
    ManufacturingOrder, ManufacturingOrderRawMaterial, ManufacturingOrderStage,
    ManufacturingInventoryTransaction, ManufacturingQualityCheck
)
from .forms import ManufacturingOrderForm, ManufacturingOrderRawMaterialFormSet
from definitions.models import ProductDefinition, WarehouseDefinition
from warehouses.models import InventoryItem


@login_required
def manufacturing_dashboard(request):
    """لوحة تحكم التصنيع"""
    # الإحصائيات العامة
    stats = {
        'total_orders': ManufacturingOrder.objects.count(),
        'active_orders': ManufacturingOrder.objects.filter(status__in=['approved', 'in_progress']).count(),
        'completed_orders': ManufacturingOrder.objects.filter(status='completed').count(),
        'pending_orders': ManufacturingOrder.objects.filter(status='draft').count(),
        'overdue_orders': ManufacturingOrder.objects.filter(
            status__in=['approved', 'in_progress'],
            expected_completion_date__lt=timezone.now()
        ).count(),
    }

    # أوامر التصنيع الحديثة
    recent_orders = ManufacturingOrder.objects.select_related(
        'final_product', 'created_by'
    ).order_by('-created_at')[:10]

    # أوامر التصنيع المتأخرة
    overdue_orders = ManufacturingOrder.objects.filter(
        status__in=['approved', 'in_progress'],
        expected_completion_date__lt=timezone.now()
    ).select_related('final_product').order_by('expected_completion_date')[:5]

    # أوامر التصنيع عالية الأولوية
    high_priority_orders = ManufacturingOrder.objects.filter(
        priority='urgent',
        status__in=['approved', 'in_progress']
    ).select_related('final_product').order_by('expected_completion_date')[:5]

    # إحصائيات التكاليف
    cost_stats = ManufacturingOrder.objects.filter(
        status='completed'
    ).aggregate(
        total_estimated_cost=Sum('estimated_raw_material_cost') + Sum('estimated_labor_cost') + Sum('estimated_overhead_cost'),
        total_actual_cost=Sum('actual_raw_material_cost') + Sum('actual_labor_cost') + Sum('actual_overhead_cost')
    )

    context = {
        'stats': stats,
        'recent_orders': recent_orders,
        'overdue_orders': overdue_orders,
        'high_priority_orders': high_priority_orders,
        'cost_stats': cost_stats,
        'page_title': 'لوحة تحكم التصنيع'
    }

    return render(request, 'manufacturing/dashboard.html', context)


@login_required
def manufacturing_order_list(request):
    """قائمة أوامر التصنيع"""
    orders = ManufacturingOrder.objects.select_related(
        'final_product', 'raw_materials_warehouse', 'finished_goods_warehouse', 'created_by'
    ).order_by('-created_at')

    # البحث والفلترة
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    priority_filter = request.GET.get('priority', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    if search_query:
        orders = orders.filter(
            Q(order_number__icontains=search_query) |
            Q(final_product__name__icontains=search_query) |
            Q(final_product__code__icontains=search_query)
        )

    if status_filter:
        orders = orders.filter(status=status_filter)

    if priority_filter:
        orders = orders.filter(priority=priority_filter)

    if date_from:
        orders = orders.filter(order_date__gte=date_from)

    if date_to:
        orders = orders.filter(order_date__lte=date_to)

    # الإحصائيات
    total_orders = orders.count()
    total_estimated_cost = orders.aggregate(
        total=Sum('estimated_raw_material_cost') + Sum('estimated_labor_cost') + Sum('estimated_overhead_cost')
    )['total'] or 0

    context = {
        'orders': orders,
        'search_query': search_query,
        'status_filter': status_filter,
        'priority_filter': priority_filter,
        'date_from': date_from,
        'date_to': date_to,
        'total_orders': total_orders,
        'total_estimated_cost': total_estimated_cost,
        'status_choices': ManufacturingOrder.STATUS_CHOICES,
        'priority_choices': ManufacturingOrder.PRIORITY_CHOICES,
        'page_title': 'أوامر التصنيع'
    }

    return render(request, 'manufacturing/order_list.html', context)


@login_required
def manufacturing_order_detail(request, order_id):
    """تفاصيل أمر التصنيع"""
    order = get_object_or_404(
        ManufacturingOrder.objects.select_related(
            'final_product', 'unit_of_measure', 'raw_materials_warehouse',
            'finished_goods_warehouse', 'created_by', 'approved_by', 'started_by', 'completed_by'
        ).prefetch_related(
            'raw_materials__raw_material',
            'production_stages',
            'inventory_transactions',
            'quality_checks'
        ),
        id=order_id
    )

    # حساب إحصائيات المواد الخام
    raw_materials_stats = {
        'total_materials': order.raw_materials.count(),
        'critical_materials': order.raw_materials.filter(is_critical=True).count(),
        'available_materials': order.raw_materials.filter(is_available=True).count(),
        'total_cost': order.raw_materials.aggregate(total=Sum('total_cost'))['total'] or 0
    }

    # حساب إحصائيات المراحل
    stages_stats = {
        'total_stages': order.production_stages.count(),
        'completed_stages': order.production_stages.filter(status='completed').count(),
        'in_progress_stages': order.production_stages.filter(status='in_progress').count(),
        'pending_stages': order.production_stages.filter(status='pending').count(),
    }

    context = {
        'order': order,
        'raw_materials_stats': raw_materials_stats,
        'stages_stats': stages_stats,
        'page_title': f'تفاصيل أمر التصنيع: {order.order_number}'
    }

    return render(request, 'manufacturing/order_detail.html', context)


@login_required
def manufacturing_order_create(request):
    """إنشاء أمر تصنيع جديد"""
    if request.method == 'POST':
        form = ManufacturingOrderForm(request.POST)
        raw_materials_formset = ManufacturingOrderRawMaterialFormSet(request.POST)

        if form.is_valid() and raw_materials_formset.is_valid():
            order = form.save(commit=False)
            order.created_by = request.user
            order.save()

            # حفظ المواد الخام
            raw_materials_formset.instance = order
            raw_materials_formset.save()

            # تحديث تكلفة المواد الخام
            order.estimated_raw_material_cost = order.raw_materials.aggregate(
                total=Sum('total_cost')
            )['total'] or 0
            order.save(update_fields=['estimated_raw_material_cost'])

            messages.success(request, f'تم إنشاء أمر التصنيع "{order.order_number}" بنجاح!')
            return redirect('manufacturing:order_detail', order_id=order.id)
    else:
        form = ManufacturingOrderForm()
        raw_materials_formset = ManufacturingOrderRawMaterialFormSet()

    context = {
        'form': form,
        'raw_materials_formset': raw_materials_formset,
        'action': 'create',
        'page_title': 'إنشاء أمر تصنيع جديد'
    }

    return render(request, 'manufacturing/order_form.html', context)


@login_required
def manufacturing_order_edit(request, order_id):
    """تعديل أمر التصنيع"""
    order = get_object_or_404(ManufacturingOrder, id=order_id)

    # التحقق من إمكانية التعديل
    if order.status not in ['draft', 'approved']:
        messages.error(request, 'لا يمكن تعديل أمر التصنيع في هذه الحالة')
        return redirect('manufacturing:order_detail', order_id=order.id)

    if request.method == 'POST':
        form = ManufacturingOrderForm(request.POST, instance=order)
        raw_materials_formset = ManufacturingOrderRawMaterialFormSet(request.POST, instance=order)

        if form.is_valid() and raw_materials_formset.is_valid():
            order = form.save()
            raw_materials_formset.save()

            # تحديث تكلفة المواد الخام
            order.estimated_raw_material_cost = order.raw_materials.aggregate(
                total=Sum('total_cost')
            )['total'] or 0
            order.save(update_fields=['estimated_raw_material_cost'])

            messages.success(request, f'تم تحديث أمر التصنيع "{order.order_number}" بنجاح!')
            return redirect('manufacturing:order_detail', order_id=order.id)
    else:
        form = ManufacturingOrderForm(instance=order)
        raw_materials_formset = ManufacturingOrderRawMaterialFormSet(instance=order)

    context = {
        'form': form,
        'raw_materials_formset': raw_materials_formset,
        'order': order,
        'action': 'edit',
        'page_title': f'تعديل أمر التصنيع: {order.order_number}'
    }

    return render(request, 'manufacturing/order_form.html', context)


@login_required
def manufacturing_order_approve(request, order_id):
    """اعتماد أمر التصنيع"""
    order = get_object_or_404(ManufacturingOrder, id=order_id)

    if order.status != 'draft':
        messages.error(request, 'لا يمكن اعتماد أمر التصنيع في هذه الحالة')
        return redirect('manufacturing:order_detail', order_id=order.id)

    if request.method == 'POST':
        # التحقق من توفر المواد الخام
        availability_check = check_raw_materials_availability(order)

        if not availability_check['all_available']:
            messages.warning(request, f'بعض المواد الخام غير متوفرة: {", ".join(availability_check["unavailable_materials"])}')

        order.status = 'approved'
        order.approved_by = request.user
        order.approved_at = timezone.now()
        order.save()

        messages.success(request, f'تم اعتماد أمر التصنيع "{order.order_number}" بنجاح!')
        return redirect('manufacturing:order_detail', order_id=order.id)

    context = {
        'order': order,
        'page_title': f'اعتماد أمر التصنيع: {order.order_number}'
    }

    return render(request, 'manufacturing/order_approve.html', context)


@login_required
def manufacturing_order_start_production(request, order_id):
    """بدء الإنتاج"""
    order = get_object_or_404(ManufacturingOrder, id=order_id)

    if order.status != 'approved':
        messages.error(request, 'يجب اعتماد أمر التصنيع أولاً قبل بدء الإنتاج')
        return redirect('manufacturing:order_detail', order_id=order.id)

    if request.method == 'POST':
        # التحقق من توفر المواد الخام وخصمها
        success = consume_raw_materials(order, request.user)

        if success:
            order.status = 'in_progress'
            order.started_by = request.user
            order.started_at = timezone.now()
            order.save()

            messages.success(request, f'تم بدء إنتاج أمر التصنيع "{order.order_number}" وخصم المواد الخام من المخزون!')
            return redirect('manufacturing:order_detail', order_id=order.id)
        else:
            messages.error(request, 'فشل في خصم المواد الخام من المخزون')

    # التحقق من توفر المواد الخام
    availability_check = check_raw_materials_availability(order)

    context = {
        'order': order,
        'availability_check': availability_check,
        'page_title': f'بدء إنتاج أمر التصنيع: {order.order_number}'
    }

    return render(request, 'manufacturing/order_start_production.html', context)


def check_raw_materials_availability(order):
    """فحص توفر المواد الخام"""
    availability_check = {
        'all_available': True,
        'unavailable_materials': [],
        'materials_status': []
    }

    for raw_material in order.raw_materials.all():
        # البحث عن المخزون المتاح
        try:
            stock = InventoryItem.objects.get(
                warehouse=order.raw_materials_warehouse,
                product=raw_material.raw_material
            )
            available_quantity = stock.quantity_on_hand
        except InventoryItem.DoesNotExist:
            available_quantity = 0

        is_available = available_quantity >= raw_material.required_quantity
        shortage = max(0, raw_material.required_quantity - available_quantity)

        material_status = {
            'raw_material': raw_material,
            'available_quantity': available_quantity,
            'required_quantity': raw_material.required_quantity,
            'is_available': is_available,
            'shortage': shortage
        }

        availability_check['materials_status'].append(material_status)

        if not is_available:
            availability_check['all_available'] = False
            availability_check['unavailable_materials'].append(raw_material.raw_material.name)

        # تحديث حالة التوفر في قاعدة البيانات
        raw_material.is_available = is_available
        raw_material.shortage_quantity = shortage
        raw_material.save(update_fields=['is_available', 'shortage_quantity'])

    return availability_check


def consume_raw_materials(order, user):
    """خصم المواد الخام من المخزون"""
    try:
        for raw_material in order.raw_materials.all():
            # البحث عن المخزون
            try:
                stock = InventoryItem.objects.get(
                    warehouse=order.raw_materials_warehouse,
                    product=raw_material.raw_material
                )
            except InventoryItem.DoesNotExist:
                return False

            # التحقق من توفر الكمية
            if stock.quantity_on_hand < raw_material.required_quantity:
                return False

            # خصم الكمية من المخزون
            stock.quantity_on_hand -= raw_material.required_quantity
            stock.save()

            # إنشاء حركة مخزون
            ManufacturingInventoryTransaction.objects.create(
                manufacturing_order=order,
                transaction_type='raw_material_consumption',
                product=raw_material.raw_material,
                warehouse=order.raw_materials_warehouse,
                quantity=raw_material.required_quantity,
                unit_of_measure=raw_material.unit_of_measure,
                unit_cost=raw_material.unit_cost,
                total_cost=raw_material.total_cost,
                reference_number=order.order_number,
                notes=f'خصم مواد خام لأمر التصنيع {order.order_number}',
                is_processed=True,
                processed_at=timezone.now(),
                processed_by=user,
                created_by=user
            )

            # تحديث الكمية المستهلكة
            raw_material.consumed_quantity = raw_material.required_quantity
            raw_material.save(update_fields=['consumed_quantity'])

        # تحديث التكلفة الفعلية للمواد الخام
        order.actual_raw_material_cost = order.raw_materials.aggregate(
            total=Sum('total_cost')
        )['total'] or 0
        order.save(update_fields=['actual_raw_material_cost'])

        return True

    except Exception as e:
        return False


@login_required
def manufacturing_order_complete(request, order_id):
    """إكمال أمر التصنيع"""
    order = get_object_or_404(ManufacturingOrder, id=order_id)

    if order.status != 'in_progress':
        messages.error(request, 'لا يمكن إكمال أمر التصنيع في هذه الحالة')
        return redirect('manufacturing:order_detail', order_id=order.id)

    if request.method == 'POST':
        # إضافة المنتج التام للمخزون
        success = add_finished_goods_to_stock(order, request.user)

        if success:
            order.status = 'completed'
            order.completed_by = request.user
            order.actual_completion_date = timezone.now()
            order.save()

            messages.success(request, f'تم إكمال أمر التصنيع "{order.order_number}" وإضافة المنتج التام للمخزون!')
            return redirect('manufacturing:order_detail', order_id=order.id)
        else:
            messages.error(request, 'فشل في إضافة المنتج التام للمخزون')

    context = {
        'order': order,
        'page_title': f'إكمال أمر التصنيع: {order.order_number}'
    }

    return render(request, 'manufacturing/order_complete.html', context)


def add_finished_goods_to_stock(order, user):
    """إضافة المنتج التام للمخزون"""
    try:
        # البحث عن المخزون أو إنشاؤه
        stock, created = InventoryItem.objects.get_or_create(
            warehouse=order.finished_goods_warehouse,
            product=order.final_product,
            defaults={
                'quantity_on_hand': 0,
                'quantity_reserved': 0,
                'minimum_stock': 0,
                'maximum_stock': 0,
                'average_cost': order.final_product.cost_price or 0,
                'last_cost': order.final_product.cost_price or 0
            }
        )

        # إضافة الكمية للمخزون
        stock.quantity_on_hand += order.quantity_to_produce

        # تحديث تكلفة الوحدة بناءً على التكلفة الفعلية
        if order.total_actual_cost > 0:
            unit_cost = order.total_actual_cost / order.quantity_to_produce
            stock.last_cost = unit_cost
            # تحديث متوسط التكلفة
            total_quantity = stock.quantity_on_hand
            if total_quantity > 0:
                stock.average_cost = ((stock.average_cost * (total_quantity - order.quantity_to_produce)) +
                                    (unit_cost * order.quantity_to_produce)) / total_quantity

        stock.save()

        # إنشاء حركة مخزون
        ManufacturingInventoryTransaction.objects.create(
            manufacturing_order=order,
            transaction_type='finished_goods_production',
            product=order.final_product,
            warehouse=order.finished_goods_warehouse,
            quantity=order.quantity_to_produce,
            unit_of_measure=order.unit_of_measure,
            unit_cost=stock.last_cost,
            total_cost=order.total_actual_cost,
            reference_number=order.order_number,
            notes=f'إنتاج منتج تام من أمر التصنيع {order.order_number}',
            is_processed=True,
            processed_at=timezone.now(),
            processed_by=user,
            created_by=user
        )

        return True

    except Exception as e:
        return False


# ===== API Views =====
@login_required
def check_raw_materials_availability_api(request, order_id):
    """API لفحص توفر المواد الخام"""
    order = get_object_or_404(ManufacturingOrder, id=order_id)
    availability_check = check_raw_materials_availability(order)

    return JsonResponse({
        'all_available': availability_check['all_available'],
        'unavailable_materials': availability_check['unavailable_materials'],
        'materials_status': [
            {
                'material_name': status['raw_material'].raw_material.name,
                'required_quantity': float(status['required_quantity']),
                'available_quantity': float(status['available_quantity']),
                'is_available': status['is_available'],
                'shortage': float(status['shortage'])
            }
            for status in availability_check['materials_status']
        ]
    })


def get_product_cost_api(request, product_id):
    """API للحصول على تكلفة المنتج ووحدة القياس"""
    try:
        print(f"[API] طلب جلب بيانات المنتج ID: {product_id}")
        print(f"[API] طريقة الطلب: {request.method}")

        # التحقق من طريقة الطلب
        if request.method != 'GET':
            return JsonResponse({
                'error': 'طريقة الطلب غير مدعومة',
                'success': False
            }, status=405)

        # البحث عن المنتج
        try:
            product = ProductDefinition.objects.get(id=product_id, is_active=True)
            print(f"[API] تم العثور على المنتج: {product.name}")
        except ProductDefinition.DoesNotExist:
            print(f"[API] المنتج غير موجود: {product_id}")
            return JsonResponse({
                'error': f'المنتج غير موجود - ID: {product_id}',
                'success': False
            }, status=404)

        # إعداد البيانات
        response_data = {
            'success': True,
            'cost_price': float(product.cost_price or 0),
            'selling_price': float(product.selling_price or 0),
            'name': product.name,
            'code': product.code,
            'product_type': product.product_type,
            'has_cost_price': bool(product.cost_price and product.cost_price > 0)
        }

        # إضافة بيانات وحدة القياس إذا كانت متاحة
        if hasattr(product, 'main_unit') and product.main_unit:
            response_data.update({
                'main_unit_id': product.main_unit.id,
                'main_unit_name': product.main_unit.name,
                'main_unit_code': product.main_unit.code,
            })
        else:
            response_data.update({
                'main_unit_id': None,
                'main_unit_name': '',
                'main_unit_code': '',
            })

        print(f"[API] إرسال بيانات المنتج: {response_data}")
        return JsonResponse(response_data)

    except Exception as e:
        error_msg = f'خطأ في الخادم: {str(e)}'
        print(f"[API] خطأ غير متوقع - Product ID: {product_id}, Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'error': error_msg,
            'success': False
        }, status=500)


@login_required
def get_product_unit_api(request, product_id):
    """API للحصول على وحدة القياس الافتراضية للمنتج"""
    try:
        product = ProductDefinition.objects.get(id=product_id)
        return JsonResponse({
            'unit_id': product.main_unit.id if product.main_unit else None,
            'unit_name': product.main_unit.name if product.main_unit else '',
            'unit_code': product.main_unit.code if product.main_unit else ''
        })
    except ProductDefinition.DoesNotExist:
        return JsonResponse({'error': 'المنتج غير موجود'}, status=404)


@login_required
def test_api_view(request):
    """صفحة اختبار API"""
    return render(request, 'manufacturing/test_api.html')


@login_required
def get_warehouse_stock_api(request, warehouse_id, product_id):
    """API للحصول على مخزون المنتج في المخزن"""
    try:
        stock = InventoryItem.objects.get(
            warehouse_id=warehouse_id,
            product_id=product_id
        )
        return JsonResponse({
            'quantity_on_hand': float(stock.quantity_on_hand),
            'quantity_reserved': float(stock.quantity_reserved),
            'available_quantity': float(stock.quantity_on_hand - stock.quantity_reserved),
            'average_cost': float(stock.average_cost),
            'last_cost': float(stock.last_cost)
        })
    except InventoryItem.DoesNotExist:
        return JsonResponse({
            'quantity_on_hand': 0,
            'quantity_reserved': 0,
            'available_quantity': 0,
            'average_cost': 0,
            'last_cost': 0
        })


@login_required
def manufacturing_order_print(request, order_id):
    """طباعة أمر التصنيع"""
    order = get_object_or_404(
        ManufacturingOrder.objects.select_related(
            'final_product', 'unit_of_measure', 'raw_materials_warehouse',
            'finished_goods_warehouse', 'created_by'
        ).prefetch_related('raw_materials__raw_material'),
        id=order_id
    )

    context = {
        'order': order,
        'print_date': timezone.now(),
        'page_title': f'طباعة أمر التصنيع: {order.order_number}'
    }

    return render(request, 'manufacturing/order_print.html', context)
