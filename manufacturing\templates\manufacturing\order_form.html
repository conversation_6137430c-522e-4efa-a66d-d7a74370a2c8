{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    
    .manufacturing-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        position: relative;
        overflow: hidden;
    }
    
    .manufacturing-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }
    
    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }
    
    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .section-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem 0 1.5rem 0;
        border-left: 4px solid #667eea;
        position: relative;
    }
    
    .section-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2d3436;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }
    
    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }
    
    .form-label {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }
    
    .required-field::after {
        content: ' *';
        color: #e74c3c;
        font-weight: bold;
    }
    
    .raw-materials-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 20px;
        padding: 2rem;
        margin: 2rem 0;
        border: 2px solid #e9ecef;
    }
    
    .materials-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        margin-top: 1.5rem;
    }
    
    .materials-table .table {
        margin: 0;
    }
    
    .materials-table .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
        text-align: center;
    }
    
    .materials-table .table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .add-material-btn {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 8px 20px rgba(86, 171, 47, 0.3);
    }
    
    .add-material-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 30px rgba(86, 171, 47, 0.4);
    }
    
    .remove-material-btn {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        border: none;
        border-radius: 8px;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .remove-material-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
    }
    
    .cost-calculator {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 15px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: center;
    }
    
    .cost-item {
        margin-bottom: 1rem;
    }
    
    .cost-label {
        font-size: 1.1rem;
        color: #1565c0;
        font-weight: 600;
    }
    
    .cost-value {
        font-size: 1.8rem;
        font-weight: 900;
        color: #0d47a1;
    }
    
    .total-cost {
        border-top: 2px solid #1976d2;
        padding-top: 1rem;
        margin-top: 1rem;
    }
    
    .total-cost .cost-value {
        font-size: 2.5rem;
        color: #0d47a1;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid #e9ecef;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 1rem 3rem;
        font-size: 1.2rem;
        font-weight: 700;
        transition: all 0.3s ease;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .btn-secondary-custom {
        background: linear-gradient(135deg, #636e72 0%, #495057 100%);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 1rem 3rem;
        font-size: 1.2rem;
        font-weight: 700;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 10px 25px rgba(99, 110, 114, 0.3);
    }
    
    .btn-secondary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(99, 110, 114, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .alert-custom {
        border-radius: 15px;
        border: none;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    .alert-danger-custom {
        background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
        color: #c53030;
        border-left: 4px solid #e53e3e;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .help-text {
        font-size: 0.9rem;
        color: #636e72;
        margin-top: 0.5rem;
        font-style: italic;
    }
    
    /* رسوم متحركة للرسائل */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-10px); }
    }

    /* تأثيرات التحميل */
    .loading-input {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* تحسينات للحقول */
    .success-input {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    }

    .warning-input {
        border-color: #ffc107 !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
    }

    .error-input {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }

        .form-container {
            padding: 2rem;
        }

        .form-row {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .btn-primary-custom,
        .btn-secondary-custom {
            width: 100%;
            max-width: 300px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // الحصول على CSRF token
    function getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    }

    // دالة لتنسيق الأرقام
    function formatNumber(number) {
        return new Intl.NumberFormat('ar-EG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(number);
    }

    // دالة مساعدة للعثور على العناصر في الصف
    function findElementInRow(row, selectors) {
        for (let selector of selectors) {
            const element = row.querySelector(selector);
            if (element) return element;
        }
        return null;
    }

    // دالة لإظهار رسالة نجاح مؤقتة
    function showSuccessMessage(row, message) {
        showMessage(row, message, 'success', 'bi-check-circle');
    }

    // دالة لإظهار رسالة تحذير مؤقتة
    function showWarningMessage(row, message) {
        showMessage(row, message, 'warning', 'bi-exclamation-triangle');
    }

    // دالة عامة لإظهار الرسائل
    function showMessage(row, message, type, icon) {
        // إزالة أي رسائل سابقة
        const existingMessages = row.querySelectorAll('.temp-message');
        existingMessages.forEach(msg => msg.remove());

        // إنشاء عنصر الرسالة
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type} alert-sm mt-1 mb-0 temp-message`;
        messageDiv.style.fontSize = '0.8rem';
        messageDiv.style.padding = '0.25rem 0.5rem';
        messageDiv.style.animation = 'fadeIn 0.3s ease-in';
        messageDiv.innerHTML = `<i class="bi ${icon} me-1"></i>${message}`;

        // إضافة الرسالة إلى أول خلية في الصف
        const firstCell = row.querySelector('td');
        if (firstCell) {
            firstCell.appendChild(messageDiv);

            // إزالة الرسالة بعد 4 ثوان
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.style.animation = 'fadeOut 0.3s ease-out';
                    setTimeout(() => {
                        if (messageDiv.parentNode) {
                            messageDiv.parentNode.removeChild(messageDiv);
                        }
                    }, 300);
                }
            }, 4000);
        }
    }

    // حساب التكاليف تلقائياً
    function calculateCosts() {
        const DEBUG_MODE = true; // يمكن تعيينها إلى false في الإنتاج
        if (DEBUG_MODE) console.log('بدء حساب التكاليف...');
        let totalMaterialCost = 0;
        let materialCount = 0;

        // حساب تكلفة المواد الخام
        document.querySelectorAll('.material-row, tr').forEach(function(row, index) {
            // تخطي الصفوف التي لا تحتوي على حقول المواد الخام
            if (!row.querySelector('input[name*="required_quantity"], input[name*="unit_cost"]')) {
                return;
            }

            // البحث عن العناصر بطرق متعددة للتأكد
            let quantityInput = findElementInRow(row, [
                '.quantity-input',
                'input[name*="required_quantity"]',
                'input[type="number"]:first-of-type'
            ]);

            let unitCostInput = findElementInRow(row, [
                '.unit-cost-input',
                'input[name*="unit_cost"]',
                'input[type="number"]:last-of-type'
            ]);

            let totalCostDisplay = row.querySelector('.total-cost-display');

            if (!quantityInput || !unitCostInput) {
                if (DEBUG_MODE) console.warn(`الصف ${index + 1}: لم يتم العثور على حقول الكمية أو السعر`);
                return;
            }

            const quantity = parseFloat(quantityInput.value) || 0;
            const unitCost = parseFloat(unitCostInput.value) || 0;
            const totalCost = quantity * unitCost;

            if (DEBUG_MODE) console.log(`الصف ${index + 1}: الكمية=${quantity}, السعر=${unitCost}, الإجمالي=${totalCost}`);

            // تحديث عرض التكلفة الإجمالية للصف
            if (totalCostDisplay) {
                totalCostDisplay.textContent = formatNumber(totalCost);

                // إضافة تأثير بصري عند التحديث
                if (totalCost > 0) {
                    totalCostDisplay.style.color = '#28a745';
                    totalCostDisplay.style.fontWeight = 'bold';
                } else {
                    totalCostDisplay.style.color = '#6c757d';
                    totalCostDisplay.style.fontWeight = 'normal';
                }
            } else {
                // محاولة إنشاء عنصر عرض التكلفة إذا لم يكن موجوداً
                const unitCostCell = unitCostInput ? unitCostInput.closest('td') : null;
                if (unitCostCell && unitCostCell.nextElementSibling) {
                    const nextCell = unitCostCell.nextElementSibling;
                    if (!nextCell.querySelector('.total-cost-display')) {
                        const costSpan = document.createElement('span');
                        costSpan.className = 'total-cost-display fw-bold text-success';
                        costSpan.textContent = formatNumber(totalCost);
                        costSpan.style.color = totalCost > 0 ? '#28a745' : '#6c757d';

                        // إضافة النص "ج.م" إذا لم يكن موجوداً
                        nextCell.innerHTML = '';
                        nextCell.appendChild(costSpan);
                        nextCell.appendChild(document.createTextNode(' ج.م'));
                    }
                } else {
                    if (DEBUG_MODE) console.warn(`الصف ${index + 1}: لم يتم العثور على عنصر عرض التكلفة الإجمالية`);
                }
            }

            totalMaterialCost += totalCost;
            materialCount++;
        });

        if (DEBUG_MODE) console.log(`إجمالي تكلفة المواد: ${totalMaterialCost} (${materialCount} مادة)`);

        // تحديث إجمالي تكلفة المواد
        const totalMaterialElement = document.getElementById('total-material-cost');
        if (totalMaterialElement) {
            totalMaterialElement.textContent = formatNumber(totalMaterialCost);
        }

        // حساب إجمالي التكلفة
        const laborCostInput = document.getElementById('id_estimated_labor_cost');
        const overheadCostInput = document.getElementById('id_estimated_overhead_cost');

        const laborCost = laborCostInput ? (parseFloat(laborCostInput.value) || 0) : 0;
        const overheadCost = overheadCostInput ? (parseFloat(overheadCostInput.value) || 0) : 0;
        const totalCost = totalMaterialCost + laborCost + overheadCost;

        if (DEBUG_MODE) console.log(`تكلفة العمالة: ${laborCost}, التكاليف الإضافية: ${overheadCost}, الإجمالي الكلي: ${totalCost}`);

        // تحديث عرض التكاليف
        const laborCostDisplay = document.getElementById('labor-cost-display');
        const overheadCostDisplay = document.getElementById('overhead-cost-display');
        const totalCostDisplay = document.getElementById('total-estimated-cost');

        if (laborCostDisplay) {
            laborCostDisplay.textContent = formatNumber(laborCost);
        }
        if (overheadCostDisplay) {
            overheadCostDisplay.textContent = formatNumber(overheadCost);
        }
        if (totalCostDisplay) {
            totalCostDisplay.textContent = formatNumber(totalCost);
        }

        if (DEBUG_MODE) console.log('انتهاء حساب التكاليف');
    }
    
    // ربط الأحداث
    document.addEventListener('input', function(e) {
        // التحقق من نوع الحقل بطرق متعددة
        const isQuantityInput = e.target.classList.contains('quantity-input') ||
                               e.target.name && e.target.name.includes('required_quantity');

        const isUnitCostInput = e.target.classList.contains('unit-cost-input') ||
                               e.target.name && e.target.name.includes('unit_cost');

        const isLaborCost = e.target.id === 'id_estimated_labor_cost';
        const isOverheadCost = e.target.id === 'id_estimated_overhead_cost';

        if (isQuantityInput || isUnitCostInput || isLaborCost || isOverheadCost) {
            // console.log(`تم تغيير حقل: ${e.target.name || e.target.id}, القيمة: ${e.target.value}`);
            calculateCosts();
        }
    });
    
    // تحديث تكلفة الوحدة ووحدة القياس عند تغيير المادة الخام
    document.addEventListener('change', function(e) {
        const isMaterialSelect = e.target.classList.contains('material-select') ||
                                (e.target.name && e.target.name.includes('raw_material'));

        console.log('Change event:', e.target.name, 'is material select:', isMaterialSelect);

        if (isMaterialSelect) {
            const productId = e.target.value;
            const row = e.target.closest('.material-row');
            console.log('Selected product ID:', productId, 'Row:', row);

            if (productId) {
                // إظهار رسالة تحميل
                const unitCostInput = row.querySelector('.unit-cost-input') ||
                                     row.querySelector('input[name*="unit_cost"]');
                const unitSelect = row.querySelector('.unit-select') ||
                                  row.querySelector('select[name*="unit_of_measure"]');

                if (unitCostInput) {
                    unitCostInput.value = '';
                    unitCostInput.placeholder = 'جاري التحميل...';
                    unitCostInput.classList.add('loading-input');
                    unitCostInput.disabled = true;
                }

                if (unitSelect) {
                    unitSelect.classList.add('loading-input');
                    unitSelect.disabled = true;
                }

                // جلب تكلفة المنتج ووحدة القياس فوراً
                fetch(`/manufacturing/api/product-cost/${productId}/`, {
                    method: 'GET',
                    headers: {
                        'X-CSRFToken': getCSRFToken(),
                        'Content-Type': 'application/json',
                    },
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.error || !data.success) {
                            console.error('خطأ في جلب بيانات المنتج:', data.error);
                            if (unitCostInput) {
                                unitCostInput.value = '0';
                                unitCostInput.placeholder = data.error || 'خطأ في جلب السعر - يرجى إدخال السعر يدوياً';
                                unitCostInput.style.backgroundColor = '#fff3cd';
                            }

                            // إظهار رسالة تحذير
                            showWarningMessage(row, data.error || 'فشل في جلب بيانات المنتج');
                            return;
                        }

                        // تحديث تكلفة الوحدة فوراً
                        if (unitCostInput) {
                            const costPrice = parseFloat(data.cost_price) || 0;
                            unitCostInput.classList.remove('loading-input');
                            unitCostInput.classList.add('success-input');
                            unitCostInput.value = costPrice.toFixed(2);
                            unitCostInput.placeholder = data.has_cost_price ? 'تم جلبه من التعريفات' : 'لا يوجد سعر محدد - يرجى الإدخال يدوياً';
                            unitCostInput.disabled = false;

                            // إعادة التنسيق الطبيعي بعد ثانيتين
                            setTimeout(() => {
                                unitCostInput.classList.remove('success-input');
                                unitCostInput.placeholder = 'سعر الوحدة';
                            }, 2000);
                        }

                        // تحديث وحدة القياس فوراً إذا كانت متاحة
                        if (unitSelect && data.main_unit_id) {
                            unitSelect.classList.remove('loading-input');
                            unitSelect.classList.add('success-input');
                            unitSelect.value = data.main_unit_id;
                            unitSelect.disabled = false;

                            // إعادة التنسيق الطبيعي بعد ثانيتين
                            setTimeout(() => {
                                unitSelect.classList.remove('success-input');
                            }, 2000);
                        } else if (unitSelect) {
                            unitSelect.classList.remove('loading-input');
                            unitSelect.disabled = false;
                        }

                        // إعادة حساب التكاليف فوراً
                        calculateCosts();

                        // إظهار رسالة نجاح مؤقتة مع تفاصيل
                        const successMessage = data.has_cost_price
                            ? `تم جلب البيانات: ${data.name} - ${formatNumber(data.cost_price)} ج.م`
                            : `تم جلب البيانات: ${data.name} - لا يوجد سعر محدد`;
                        showSuccessMessage(row, successMessage);
                    })
                    .catch(error => {
                        console.error('خطأ في الاتصال بالخادم:', error);
                        console.error('تفاصيل الخطأ:', {
                            productId: productId,
                            url: `/manufacturing/api/product-cost/${productId}/`,
                            error: error.message
                        });

                        if (unitCostInput) {
                            unitCostInput.classList.remove('loading-input');
                            unitCostInput.classList.add('error-input');
                            unitCostInput.value = '0';
                            unitCostInput.placeholder = 'خطأ في الاتصال - يرجى إدخال السعر يدوياً';
                            unitCostInput.disabled = false;
                        }

                        if (unitSelect) {
                            unitSelect.classList.remove('loading-input');
                            unitSelect.classList.add('error-input');
                            unitSelect.disabled = false;
                        }

                        // إظهار رسالة خطأ مفصلة
                        const errorMessage = error.message.includes('Failed to fetch')
                            ? 'فشل في الاتصال بالخادم - تحقق من أن الخادم يعمل'
                            : `خطأ: ${error.message}`;
                        showWarningMessage(row, errorMessage);

                        // إعادة التنسيق الطبيعي بعد 3 ثوان
                        setTimeout(() => {
                            if (unitCostInput) unitCostInput.classList.remove('error-input');
                            if (unitSelect) unitSelect.classList.remove('error-input');
                        }, 3000);
                    });
            } else {
                // إذا لم يتم اختيار منتج، إعادة تعيين القيم
                const unitCostInput = row.querySelector('.unit-cost-input') ||
                                     row.querySelector('input[name*="unit_cost"]');
                const unitSelect = row.querySelector('.unit-select') ||
                                  row.querySelector('select[name*="unit_of_measure"]');

                if (unitCostInput) {
                    unitCostInput.value = '';
                    unitCostInput.placeholder = 'اختر المادة الخام أولاً';
                    unitCostInput.style.backgroundColor = '';
                }

                if (unitSelect) {
                    unitSelect.value = '';
                    unitSelect.style.backgroundColor = '';
                }

                calculateCosts();
            }
        }
    });
    
    // إضافة مادة خام جديدة
    document.getElementById('add-material-btn').addEventListener('click', function() {
        const formsetPrefix = 'raw_materials';
        const totalForms = document.getElementById(`id_${formsetPrefix}-TOTAL_FORMS`);
        console.log('Total forms element:', totalForms);
        console.log('Current total forms value:', totalForms ? totalForms.value : 'not found');
        const formNum = parseInt(totalForms.value);

        // نسخ آخر صف وتحديث الفهارس
        const lastRow = document.querySelector('.material-row:last-child');
        if (!lastRow) {
            console.error('لا يمكن العثور على صف المواد الخام');
            return;
        }

        const newRow = lastRow.cloneNode(true);

        // تحديث الأسماء والمعرفات
        newRow.querySelectorAll('input, select').forEach(function(input) {
            const name = input.name.replace(/-\d+-/, `-${formNum}-`);
            const id = input.id.replace(/-\d+-/, `-${formNum}-`);
            input.name = name;
            input.id = id;

            // إعادة تعيين القيم
            if (input.type === 'checkbox') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });

        // إعادة تعيين عرض التكلفة الإجمالية
        const totalCostDisplay = newRow.querySelector('.total-cost-display');
        if (totalCostDisplay) {
            totalCostDisplay.textContent = '0.00';
        }

        // إضافة الصف الجديد
        document.querySelector('.materials-tbody').appendChild(newRow);

        // تحديث عدد النماذج
        totalForms.value = formNum + 1;

        // إعادة حساب التكاليف
        calculateCosts();
    });
    
    // حذف مادة خام
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-material-btn')) {
            const row = e.target.closest('.material-row');
            const deleteCheckbox = row.querySelector('input[name$="-DELETE"]');
            if (deleteCheckbox) {
                deleteCheckbox.checked = true;
                row.style.display = 'none';
            } else {
                row.remove();
            }
            calculateCosts();
        }
    });
    
    // تحديث أسعار المواد الموجودة عند تحميل الصفحة
    function updateExistingMaterialPrices() {
        document.querySelectorAll('.material-row').forEach(function(row, index) {
            const materialSelect = row.querySelector('.material-select');
            const unitCostInput = row.querySelector('.unit-cost-input');
            const unitSelect = row.querySelector('.unit-select');

            if (materialSelect && materialSelect.value) {
                const productId = materialSelect.value;

                // تأخير طفيف لتجنب إرسال طلبات متعددة في نفس الوقت
                setTimeout(() => {
                    fetch(`/manufacturing/api/product-cost/${productId}/`, {
                        method: 'GET',
                        headers: {
                            'X-CSRFToken': getCSRFToken(),
                            'Content-Type': 'application/json',
                        },
                    })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (!data.error) {
                                // تحديث السعر إذا لم يكن موجود أو كان صفر
                                if (unitCostInput && (!unitCostInput.value || unitCostInput.value == '0')) {
                                    const costPrice = parseFloat(data.cost_price) || 0;
                                    unitCostInput.value = costPrice.toFixed(2);
                                    unitCostInput.style.backgroundColor = '#e8f5e8';

                                    setTimeout(() => {
                                        unitCostInput.style.backgroundColor = '';
                                    }, 1500);
                                }

                                // تحديث وحدة القياس إذا لم تكن محددة
                                if (unitSelect && !unitSelect.value && data.main_unit_id) {
                                    unitSelect.value = data.main_unit_id;
                                    unitSelect.style.backgroundColor = '#e8f5e8';

                                    setTimeout(() => {
                                        unitSelect.style.backgroundColor = '';
                                    }, 1500);
                                }

                                calculateCosts();
                            }
                        })
                        .catch(error => {
                            console.error('خطأ في جلب سعر المنتج:', error);
                        });
                }, index * 100); // تأخير 100ms بين كل طلب
            }
        });
    }

    // دالة لتشخيص المشكلة
    function debugFormElements() {
        console.log('=== تشخيص عناصر النموذج ===');

        const materialRows = document.querySelectorAll('.material-row');
        console.log(`عدد صفوف المواد الخام: ${materialRows.length}`);

        materialRows.forEach((row, index) => {
            console.log(`--- الصف ${index + 1} ---`);

            const quantityInput = row.querySelector('.quantity-input') || row.querySelector('input[name*="required_quantity"]');
            const unitCostInput = row.querySelector('.unit-cost-input') || row.querySelector('input[name*="unit_cost"]');
            const materialSelect = row.querySelector('.material-select') || row.querySelector('select[name*="raw_material"]');
            const totalCostDisplay = row.querySelector('.total-cost-display');

            console.log('حقل الكمية:', quantityInput ? `موجود (${quantityInput.value})` : 'غير موجود');
            console.log('حقل السعر:', unitCostInput ? `موجود (${unitCostInput.value})` : 'غير موجود');
            console.log('قائمة المواد:', materialSelect ? `موجود (${materialSelect.value})` : 'غير موجود');
            console.log('عرض التكلفة:', totalCostDisplay ? 'موجود' : 'غير موجود');

            if (quantityInput) console.log('اسم حقل الكمية:', quantityInput.name);
            if (unitCostInput) console.log('اسم حقل السعر:', unitCostInput.name);
            if (materialSelect) console.log('اسم قائمة المواد:', materialSelect.name);
        });

        console.log('=== انتهاء التشخيص ===');
    }

    // دالة لاختبار الاتصال بـ API
    function testAPIConnection() {
        console.log('اختبار الاتصال بـ API...');

        // اختبار بمعرف وهمي للتأكد من أن API يرد
        fetch('/manufacturing/api/product-cost/999999/', {
            method: 'GET',
            headers: {
                'X-CSRFToken': getCSRFToken(),
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            console.log(`حالة الاستجابة: ${response.status}`);
            if (response.status === 404) {
                console.log('✅ API يعمل بشكل صحيح (404 متوقع للمعرف الوهمي)');
            } else {
                console.log(`⚠️ استجابة غير متوقعة: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('بيانات الاستجابة:', data);
        })
        .catch(error => {
            console.error('❌ فشل في الاتصال بـ API:', error);
            console.error('تأكد من أن الخادم يعمل على http://localhost:8000');
        });
    }

    // تشخيص العناصر أولاً
    debugFormElements();

    // اختبار الاتصال بـ API
    testAPIConnection();

    // تحديث الأسعار والحساب عند تحميل الصفحة
    updateExistingMaterialPrices();
    calculateCosts();
});
</script>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="manufacturing-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:order_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <form method="post" novalidate>
        {% csrf_token %}

        <div class="form-container">
            <!-- معلومات أساسية -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    المعلومات الأساسية
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.order_number.id_for_label }}" class="form-label">{{ form.order_number.label }}</label>
                    {{ form.order_number }}
                    {% if form.order_number.errors %}
                        <div class="alert alert-danger-custom">{{ form.order_number.errors.0 }}</div>
                    {% endif %}
                    <div class="help-text">سيتم إنشاؤه تلقائياً إذا ترك فارغاً</div>
                </div>

                <div class="form-group">
                    <label for="{{ form.order_date.id_for_label }}" class="form-label required-field">{{ form.order_date.label }}</label>
                    {{ form.order_date }}
                    {% if form.order_date.errors %}
                        <div class="alert alert-danger-custom">{{ form.order_date.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.expected_completion_date.id_for_label }}" class="form-label required-field">{{ form.expected_completion_date.label }}</label>
                    {{ form.expected_completion_date }}
                    {% if form.expected_completion_date.errors %}
                        <div class="alert alert-danger-custom">{{ form.expected_completion_date.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.priority.id_for_label }}" class="form-label">{{ form.priority.label }}</label>
                    {{ form.priority }}
                    {% if form.priority.errors %}
                        <div class="alert alert-danger-custom">{{ form.priority.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- معلومات المنتج -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-box"></i>
                    </div>
                    معلومات المنتج
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.final_product.id_for_label }}" class="form-label required-field">{{ form.final_product.label }}</label>
                    {{ form.final_product }}
                    {% if form.final_product.errors %}
                        <div class="alert alert-danger-custom">{{ form.final_product.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.quantity_to_produce.id_for_label }}" class="form-label required-field">{{ form.quantity_to_produce.label }}</label>
                    {{ form.quantity_to_produce }}
                    {% if form.quantity_to_produce.errors %}
                        <div class="alert alert-danger-custom">{{ form.quantity_to_produce.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.unit_of_measure.id_for_label }}" class="form-label required-field">{{ form.unit_of_measure.label }}</label>
                    {{ form.unit_of_measure }}
                    {% if form.unit_of_measure.errors %}
                        <div class="alert alert-danger-custom">{{ form.unit_of_measure.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- معلومات المخازن -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    معلومات المخازن
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.raw_materials_warehouse.id_for_label }}" class="form-label required-field">{{ form.raw_materials_warehouse.label }}</label>
                    {{ form.raw_materials_warehouse }}
                    {% if form.raw_materials_warehouse.errors %}
                        <div class="alert alert-danger-custom">{{ form.raw_materials_warehouse.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.finished_goods_warehouse.id_for_label }}" class="form-label required-field">{{ form.finished_goods_warehouse.label }}</label>
                    {{ form.finished_goods_warehouse }}
                    {% if form.finished_goods_warehouse.errors %}
                        <div class="alert alert-danger-custom">{{ form.finished_goods_warehouse.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- المواد الخام -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-layers"></i>
                    </div>
                    المواد الخام المطلوبة
                </h3>
            </div>

            <div class="raw-materials-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">قائمة المواد الخام</h5>
                    <button type="button" id="add-material-btn" class="add-material-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة مادة خام
                    </button>
                </div>

                {{ raw_materials_formset.management_form }}

                <!-- Debug Information -->
                {% if raw_materials_formset.forms %}
                    <div class="alert alert-info">
                        <small>عدد نماذج المواد الخام: {{ raw_materials_formset.forms|length }} | Prefix: {{ raw_materials_formset.prefix }}</small>
                    </div>
                {% else %}
                    <div class="alert alert-warning">
                        <small>لا توجد نماذج للمواد الخام | Prefix: {{ raw_materials_formset.prefix }}</small>
                    </div>
                {% endif %}

                <div class="materials-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المادة الخام</th>
                                <th>الكمية المطلوبة</th>
                                <th>وحدة القياس</th>
                                <th>تكلفة الوحدة</th>
                                <th>إجمالي التكلفة</th>
                                <th>مادة حرجة</th>
                                <th>ملاحظات</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="materials-tbody">
                            {% for form in raw_materials_formset %}
                            <tr class="material-row">
                                <td>
                                    {{ form.raw_material }}
                                    {% if form.raw_material.errors %}
                                        <div class="text-danger small">{{ form.raw_material.errors.0 }}</div>
                                    {% endif %}
                                    <!-- Debug: عدد الخيارات المتاحة -->
                                    <small class="text-muted">
                                        خيارات متاحة: {{ form.raw_material.field.queryset.count }}
                                        {% if form.raw_material.field.queryset.count > 0 %}
                                            <br>أول مادة: {{ form.raw_material.field.queryset.first.name }}
                                        {% endif %}
                                    </small>
                                </td>
                                <td>
                                    {{ form.required_quantity }}
                                    {% if form.required_quantity.errors %}
                                        <div class="text-danger small">{{ form.required_quantity.errors.0 }}</div>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ form.unit_of_measure }}
                                    {% if form.unit_of_measure.errors %}
                                        <div class="text-danger small">{{ form.unit_of_measure.errors.0 }}</div>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ form.unit_cost }}
                                    {% if form.unit_cost.errors %}
                                        <div class="text-danger small">{{ form.unit_cost.errors.0 }}</div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="total-cost-display fw-bold text-success">0.00</span> ج.م
                                </td>
                                <td class="text-center">
                                    {{ form.is_critical }}
                                </td>
                                <td>
                                    {{ form.notes }}
                                </td>
                                <td class="text-center">
                                    <button type="button" class="remove-material-btn" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {{ form.DELETE }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- التكاليف -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    التكاليف المقدرة
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.estimated_labor_cost.id_for_label }}" class="form-label">{{ form.estimated_labor_cost.label }}</label>
                    {{ form.estimated_labor_cost }}
                    {% if form.estimated_labor_cost.errors %}
                        <div class="alert alert-danger-custom">{{ form.estimated_labor_cost.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.estimated_overhead_cost.id_for_label }}" class="form-label">{{ form.estimated_overhead_cost.label }}</label>
                    {{ form.estimated_overhead_cost }}
                    {% if form.estimated_overhead_cost.errors %}
                        <div class="alert alert-danger-custom">{{ form.estimated_overhead_cost.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- حاسبة التكاليف -->
            <div class="cost-calculator">
                <h4 class="mb-4">حاسبة التكاليف الشاملة</h4>
                <div class="row">
                    <div class="col-md-3">
                        <div class="cost-item">
                            <div class="cost-label">تكلفة المواد الخام</div>
                            <div class="cost-value"><span id="total-material-cost">0.00</span> ج.م</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cost-item">
                            <div class="cost-label">تكلفة العمالة</div>
                            <div class="cost-value"><span id="labor-cost-display">0.00</span> ج.م</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cost-item">
                            <div class="cost-label">التكاليف الإضافية</div>
                            <div class="cost-value"><span id="overhead-cost-display">0.00</span> ج.م</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cost-item total-cost">
                            <div class="cost-label">إجمالي التكلفة</div>
                            <div class="cost-value"><span id="total-estimated-cost">0.00</span> ج.م</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-file-text"></i>
                    </div>
                    معلومات إضافية
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                    {{ form.notes }}
                    {% if form.notes.errors %}
                        <div class="alert alert-danger-custom">{{ form.notes.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.special_instructions.id_for_label }}" class="form-label">{{ form.special_instructions.label }}</label>
                    {{ form.special_instructions }}
                    {% if form.special_instructions.errors %}
                        <div class="alert alert-danger-custom">{{ form.special_instructions.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.quality_requirements.id_for_label }}" class="form-label">{{ form.quality_requirements.label }}</label>
                    {{ form.quality_requirements }}
                    {% if form.quality_requirements.errors %}
                        <div class="alert alert-danger-custom">{{ form.quality_requirements.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="action-buttons">
                <button type="submit" class="btn-primary-custom">
                    <i class="bi bi-check-circle me-2"></i>
                    {% if action == 'create' %}إنشاء أمر التصنيع{% else %}حفظ التغييرات{% endif %}
                </button>
                <a href="{% url 'manufacturing:order_list' %}" class="btn-secondary-custom">
                    <i class="bi bi-x-circle me-2"></i>إلغاء
                </a>
            </div>

            {% if form.non_field_errors %}
                <div class="alert alert-danger-custom mt-3">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}
        </div>
    </form>
</div>
{% endblock %}
