{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    
    .manufacturing-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        position: relative;
        overflow: hidden;
    }
    
    .manufacturing-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }
    
    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }
    
    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .section-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem 0 1.5rem 0;
        border-left: 4px solid #667eea;
        position: relative;
    }
    
    .section-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2d3436;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }
    
    .form-control, .form-select {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }
    
    .form-label {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }
    
    .required-field::after {
        content: ' *';
        color: #e74c3c;
        font-weight: bold;
    }
    
    .raw-materials-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 20px;
        padding: 2rem;
        margin: 2rem 0;
        border: 2px solid #e9ecef;
    }
    
    .materials-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        margin-top: 1.5rem;
    }
    
    .materials-table .table {
        margin: 0;
    }
    
    .materials-table .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem;
        font-weight: 600;
        text-align: center;
    }
    
    .materials-table .table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .add-material-btn {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 8px 20px rgba(86, 171, 47, 0.3);
    }
    
    .add-material-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 30px rgba(86, 171, 47, 0.4);
    }
    
    .remove-material-btn {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        border: none;
        border-radius: 8px;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .remove-material-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
    }
    
    .cost-calculator {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 15px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: center;
    }
    
    .cost-item {
        margin-bottom: 1rem;
    }
    
    .cost-label {
        font-size: 1.1rem;
        color: #1565c0;
        font-weight: 600;
    }
    
    .cost-value {
        font-size: 1.8rem;
        font-weight: 900;
        color: #0d47a1;
    }
    
    .total-cost {
        border-top: 2px solid #1976d2;
        padding-top: 1rem;
        margin-top: 1rem;
    }
    
    .total-cost .cost-value {
        font-size: 2.5rem;
        color: #0d47a1;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid #e9ecef;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 1rem 3rem;
        font-size: 1.2rem;
        font-weight: 700;
        transition: all 0.3s ease;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .btn-secondary-custom {
        background: linear-gradient(135deg, #636e72 0%, #495057 100%);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 1rem 3rem;
        font-size: 1.2rem;
        font-weight: 700;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 10px 25px rgba(99, 110, 114, 0.3);
    }
    
    .btn-secondary-custom:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(99, 110, 114, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .alert-custom {
        border-radius: 15px;
        border: none;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    .alert-danger-custom {
        background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
        color: #c53030;
        border-left: 4px solid #e53e3e;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .help-text {
        font-size: 0.9rem;
        color: #636e72;
        margin-top: 0.5rem;
        font-style: italic;
    }
    
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .form-container {
            padding: 2rem;
        }
        
        .form-row {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .btn-primary-custom,
        .btn-secondary-custom {
            width: 100%;
            max-width: 300px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // حساب التكاليف تلقائياً
    function calculateCosts() {
        let totalMaterialCost = 0;
        
        // حساب تكلفة المواد الخام
        document.querySelectorAll('.material-row').forEach(function(row) {
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const unitCost = parseFloat(row.querySelector('.unit-cost-input').value) || 0;
            const totalCost = quantity * unitCost;
            
            row.querySelector('.total-cost-display').textContent = totalCost.toFixed(2);
            totalMaterialCost += totalCost;
        });
        
        // تحديث إجمالي تكلفة المواد
        document.getElementById('total-material-cost').textContent = totalMaterialCost.toFixed(2);
        
        // حساب إجمالي التكلفة
        const laborCost = parseFloat(document.getElementById('id_estimated_labor_cost').value) || 0;
        const overheadCost = parseFloat(document.getElementById('id_estimated_overhead_cost').value) || 0;
        const totalCost = totalMaterialCost + laborCost + overheadCost;
        
        document.getElementById('total-estimated-cost').textContent = totalCost.toFixed(2);
    }
    
    // ربط الأحداث
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('quantity-input') || 
            e.target.classList.contains('unit-cost-input') ||
            e.target.id === 'id_estimated_labor_cost' ||
            e.target.id === 'id_estimated_overhead_cost') {
            calculateCosts();
        }
    });
    
    // تحديث تكلفة الوحدة عند تغيير المادة الخام
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('material-select')) {
            const productId = e.target.value;
            if (productId) {
                fetch(`/manufacturing/api/product-cost/${productId}/`)
                    .then(response => response.json())
                    .then(data => {
                        const row = e.target.closest('.material-row');
                        row.querySelector('.unit-cost-input').value = data.cost_price;
                        calculateCosts();
                    });
            }
        }
    });
    
    // إضافة مادة خام جديدة
    document.getElementById('add-material-btn').addEventListener('click', function() {
        const formsetPrefix = 'raw_materials';
        const totalForms = document.getElementById(`id_${formsetPrefix}-TOTAL_FORMS`);
        const formNum = parseInt(totalForms.value);
        
        // نسخ آخر صف وتحديث الفهارس
        const lastRow = document.querySelector('.material-row:last-child');
        const newRow = lastRow.cloneNode(true);
        
        // تحديث الأسماء والمعرفات
        newRow.querySelectorAll('input, select').forEach(function(input) {
            const name = input.name.replace(/-\d+-/, `-${formNum}-`);
            const id = input.id.replace(/-\d+-/, `-${formNum}-`);
            input.name = name;
            input.id = id;
            input.value = '';
        });
        
        // إضافة الصف الجديد
        document.querySelector('.materials-tbody').appendChild(newRow);
        
        // تحديث عدد النماذج
        totalForms.value = formNum + 1;
    });
    
    // حذف مادة خام
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-material-btn')) {
            const row = e.target.closest('.material-row');
            const deleteCheckbox = row.querySelector('input[name$="-DELETE"]');
            if (deleteCheckbox) {
                deleteCheckbox.checked = true;
                row.style.display = 'none';
            } else {
                row.remove();
            }
            calculateCosts();
        }
    });
    
    // حساب التكاليف عند تحميل الصفحة
    calculateCosts();
});
</script>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="manufacturing-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:order_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <form method="post" novalidate>
        {% csrf_token %}

        <div class="form-container">
            <!-- معلومات أساسية -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    المعلومات الأساسية
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.order_number.id_for_label }}" class="form-label">{{ form.order_number.label }}</label>
                    {{ form.order_number }}
                    {% if form.order_number.errors %}
                        <div class="alert alert-danger-custom">{{ form.order_number.errors.0 }}</div>
                    {% endif %}
                    <div class="help-text">سيتم إنشاؤه تلقائياً إذا ترك فارغاً</div>
                </div>

                <div class="form-group">
                    <label for="{{ form.order_date.id_for_label }}" class="form-label required-field">{{ form.order_date.label }}</label>
                    {{ form.order_date }}
                    {% if form.order_date.errors %}
                        <div class="alert alert-danger-custom">{{ form.order_date.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.expected_completion_date.id_for_label }}" class="form-label required-field">{{ form.expected_completion_date.label }}</label>
                    {{ form.expected_completion_date }}
                    {% if form.expected_completion_date.errors %}
                        <div class="alert alert-danger-custom">{{ form.expected_completion_date.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.priority.id_for_label }}" class="form-label">{{ form.priority.label }}</label>
                    {{ form.priority }}
                    {% if form.priority.errors %}
                        <div class="alert alert-danger-custom">{{ form.priority.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- معلومات المنتج -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-box"></i>
                    </div>
                    معلومات المنتج
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.final_product.id_for_label }}" class="form-label required-field">{{ form.final_product.label }}</label>
                    {{ form.final_product }}
                    {% if form.final_product.errors %}
                        <div class="alert alert-danger-custom">{{ form.final_product.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.quantity_to_produce.id_for_label }}" class="form-label required-field">{{ form.quantity_to_produce.label }}</label>
                    {{ form.quantity_to_produce }}
                    {% if form.quantity_to_produce.errors %}
                        <div class="alert alert-danger-custom">{{ form.quantity_to_produce.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.unit_of_measure.id_for_label }}" class="form-label required-field">{{ form.unit_of_measure.label }}</label>
                    {{ form.unit_of_measure }}
                    {% if form.unit_of_measure.errors %}
                        <div class="alert alert-danger-custom">{{ form.unit_of_measure.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- معلومات المخازن -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    معلومات المخازن
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.raw_materials_warehouse.id_for_label }}" class="form-label required-field">{{ form.raw_materials_warehouse.label }}</label>
                    {{ form.raw_materials_warehouse }}
                    {% if form.raw_materials_warehouse.errors %}
                        <div class="alert alert-danger-custom">{{ form.raw_materials_warehouse.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.finished_goods_warehouse.id_for_label }}" class="form-label required-field">{{ form.finished_goods_warehouse.label }}</label>
                    {{ form.finished_goods_warehouse }}
                    {% if form.finished_goods_warehouse.errors %}
                        <div class="alert alert-danger-custom">{{ form.finished_goods_warehouse.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- المواد الخام -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-layers"></i>
                    </div>
                    المواد الخام المطلوبة
                </h3>
            </div>

            <div class="raw-materials-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">قائمة المواد الخام</h5>
                    <button type="button" id="add-material-btn" class="add-material-btn">
                        <i class="bi bi-plus-circle me-2"></i>إضافة مادة خام
                    </button>
                </div>

                {{ raw_materials_formset.management_form }}

                <div class="materials-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المادة الخام</th>
                                <th>الكمية المطلوبة</th>
                                <th>وحدة القياس</th>
                                <th>تكلفة الوحدة</th>
                                <th>إجمالي التكلفة</th>
                                <th>مادة حرجة</th>
                                <th>ملاحظات</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="materials-tbody">
                            {% for form in raw_materials_formset %}
                            <tr class="material-row">
                                <td>
                                    {{ form.raw_material }}
                                    {% if form.raw_material.errors %}
                                        <div class="text-danger small">{{ form.raw_material.errors.0 }}</div>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ form.required_quantity }}
                                    {% if form.required_quantity.errors %}
                                        <div class="text-danger small">{{ form.required_quantity.errors.0 }}</div>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ form.unit_of_measure }}
                                    {% if form.unit_of_measure.errors %}
                                        <div class="text-danger small">{{ form.unit_of_measure.errors.0 }}</div>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ form.unit_cost }}
                                    {% if form.unit_cost.errors %}
                                        <div class="text-danger small">{{ form.unit_cost.errors.0 }}</div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="total-cost-display fw-bold text-success">0.00</span> جنيه
                                </td>
                                <td class="text-center">
                                    {{ form.is_critical }}
                                </td>
                                <td>
                                    {{ form.notes }}
                                </td>
                                <td class="text-center">
                                    <button type="button" class="remove-material-btn" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {{ form.DELETE }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- التكاليف -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    التكاليف المقدرة
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.estimated_labor_cost.id_for_label }}" class="form-label">{{ form.estimated_labor_cost.label }}</label>
                    {{ form.estimated_labor_cost }}
                    {% if form.estimated_labor_cost.errors %}
                        <div class="alert alert-danger-custom">{{ form.estimated_labor_cost.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.estimated_overhead_cost.id_for_label }}" class="form-label">{{ form.estimated_overhead_cost.label }}</label>
                    {{ form.estimated_overhead_cost }}
                    {% if form.estimated_overhead_cost.errors %}
                        <div class="alert alert-danger-custom">{{ form.estimated_overhead_cost.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- حاسبة التكاليف -->
            <div class="cost-calculator">
                <h4 class="mb-4">حاسبة التكاليف الشاملة</h4>
                <div class="row">
                    <div class="col-md-3">
                        <div class="cost-item">
                            <div class="cost-label">تكلفة المواد الخام</div>
                            <div class="cost-value"><span id="total-material-cost">0.00</span> جنيه</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cost-item">
                            <div class="cost-label">تكلفة العمالة</div>
                            <div class="cost-value"><span id="labor-cost-display">0.00</span> جنيه</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cost-item">
                            <div class="cost-label">التكاليف الإضافية</div>
                            <div class="cost-value"><span id="overhead-cost-display">0.00</span> جنيه</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="cost-item total-cost">
                            <div class="cost-label">إجمالي التكلفة</div>
                            <div class="cost-value"><span id="total-estimated-cost">0.00</span> جنيه</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-file-text"></i>
                    </div>
                    معلومات إضافية
                </h3>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                    {{ form.notes }}
                    {% if form.notes.errors %}
                        <div class="alert alert-danger-custom">{{ form.notes.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.special_instructions.id_for_label }}" class="form-label">{{ form.special_instructions.label }}</label>
                    {{ form.special_instructions }}
                    {% if form.special_instructions.errors %}
                        <div class="alert alert-danger-custom">{{ form.special_instructions.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.quality_requirements.id_for_label }}" class="form-label">{{ form.quality_requirements.label }}</label>
                    {{ form.quality_requirements }}
                    {% if form.quality_requirements.errors %}
                        <div class="alert alert-danger-custom">{{ form.quality_requirements.errors.0 }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="action-buttons">
                <button type="submit" class="btn-primary-custom">
                    <i class="bi bi-check-circle me-2"></i>
                    {% if action == 'create' %}إنشاء أمر التصنيع{% else %}حفظ التغييرات{% endif %}
                </button>
                <a href="{% url 'manufacturing:order_list' %}" class="btn-secondary-custom">
                    <i class="bi bi-x-circle me-2"></i>إلغاء
                </a>
            </div>

            {% if form.non_field_errors %}
                <div class="alert alert-danger-custom mt-3">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}
        </div>
    </form>
</div>
{% endblock %}
