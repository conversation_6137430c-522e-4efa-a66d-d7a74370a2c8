{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .manufacturing-form-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 1rem 0;
    }

    .form-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }

    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
    }

    .form-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .form-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .form-header .subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
        position: relative;
        z-index: 1;
    }

    .form-body {
        padding: 3rem;
    }

    .section-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .section-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
    }

    .section-title {
        color: #495057;
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 3px solid #667eea;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-left: 0.5rem;
        color: #667eea;
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        position: relative;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.95rem;
    }

    .required-field::after {
        content: ' *';
        color: #e74c3c;
        font-weight: bold;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    .form-control-lg, .form-select-lg {
        padding: 1rem 1.25rem;
        font-size: 1.1rem;
        font-weight: 500;
    }

    .materials-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 20px;
        padding: 2rem;
        margin: 2rem 0;
        border: 2px solid #e9ecef;
        position: relative;
        overflow: hidden;
    }

    .materials-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
        pointer-events: none;
    }

    .materials-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        position: relative;
        z-index: 1;
    }

    .materials-title {
        color: #495057;
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .materials-title i {
        margin-left: 0.5rem;
        color: #667eea;
    }

    .add-material-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        border-radius: 50px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 0.95rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .add-material-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    }

    .add-material-btn i {
        margin-left: 0.5rem;
    }

    .materials-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        position: relative;
        z-index: 1;
        width: 100%;
        margin: 0;
    }

    .available-quantity-display {
        text-align: center;
        padding: 0.5rem;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        min-height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
    }

    .available-quantity-display .text-success {
        font-weight: 600;
        font-size: 1.1rem;
    }

    .available-quantity-display .text-warning {
        font-weight: 600;
        color: #fd7e14 !important;
    }

    /* تحسين توزيع عرض الأعمدة للعرض الكامل */
    .materials-table th:nth-child(1) { width: 16%; } /* المخزن */
    .materials-table th:nth-child(2) { width: 28%; } /* المادة الخام */
    .materials-table th:nth-child(3) { width: 13%; } /* الكمية المتاحة */
    .materials-table th:nth-child(4) { width: 13%; } /* الكمية المطلوبة */
    .materials-table th:nth-child(5) { width: 10%; } /* وحدة القياس */
    .materials-table th:nth-child(6) { width: 10%; } /* تكلفة الوحدة */
    .materials-table th:nth-child(7) { width: 12%; } /* إجمالي التكلفة */
    .materials-table th:nth-child(8) { width: 8%; }  /* إجراءات */

    /* تحسين مظهر الخلايا */
    .materials-table td {
        padding: 0.75rem 0.5rem;
        vertical-align: middle;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .materials-table th {
        padding: 1rem 0.5rem;
        font-size: 0.85rem;
        font-weight: 600;
        text-align: center;
    }

    /* تحسين حقول الإدخال */
    .materials-table .form-select,
    .materials-table .form-control {
        font-size: 0.85rem;
        padding: 0.5rem;
        border-radius: 6px;
    }

    /* تحسين عرض الكمية المتاحة */
    .available-quantity-display {
        font-size: 0.8rem;
        padding: 0.4rem;
        min-height: 2rem;
    }

    .table {
        margin: 0;
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        table-layout: fixed;
        font-size: 0.9rem;
    }

    .table thead th {
        background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
        color: white;
        font-weight: 600;
        padding: 1rem;
        border: none;
        text-align: center;
        font-size: 0.9rem;
    }

    .table thead th:first-child {
        border-top-right-radius: 15px;
    }

    .table thead th:last-child {
        border-top-left-radius: 15px;
    }

    .table tbody td {
        padding: 1rem;
        border: none;
        border-bottom: 1px solid #f8f9fa;
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .material-row {
        transition: all 0.3s ease;
    }

    .remove-material-btn {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border: none;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .remove-material-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
    }

    .total-cost-display {
        font-weight: 700;
        color: #28a745;
        font-size: 1.1rem;
    }

    .cost-summary {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-top: 2rem;
        text-align: center;
    }

    .cost-summary h4 {
        margin-bottom: 1rem;
        font-weight: 700;
    }

    .cost-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .cost-total {
        border-top: 2px solid rgba(255, 255, 255, 0.3);
        padding-top: 1rem;
        margin-top: 1rem;
        font-size: 1.3rem;
        font-weight: 700;
    }

    .form-actions {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        padding: 2rem;
        border-radius: 0 0 20px 20px;
        text-align: center;
        border-top: 1px solid #e9ecef;
    }

    .btn-primary {
        background: #007bff;
        border: none;
        border-radius: 5px;
        padding: 12px 30px;
        font-weight: 600;
        font-size: 16px;
        margin: 0 10px;
        color: white;
        cursor: pointer;
    }

    .btn-primary:hover {
        background: #0056b3;
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 5px;
        padding: 12px 30px;
        font-weight: 600;
        font-size: 16px;
        margin: 0 10px;
        color: white;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary:hover {
        background: #545b62;
        color: white;
        text-decoration: none;
    }

    .alert-danger-custom {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border: 1px solid #f5c6cb;
        border-radius: 10px;
        padding: 0.75rem;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }

    .loading-input {
        background: linear-gradient(90deg, #f8f9fa 25%, #e9ecef 50%, #f8f9fa 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    .success-input {
        border-color: #28a745 !important;
        background-color: #d4edda !important;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    .form-check-input {
        width: 1.2rem;
        height: 1.2rem;
        border-radius: 0.25rem;
        border: 2px solid #667eea;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .materials-header {
            flex-direction: column;
            gap: 1rem;
        }
        
        .form-actions {
            padding: 1rem;
        }
        
        .btn-primary, .btn-secondary {
            width: 100%;
            margin: 0.5rem 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="manufacturing-form-container">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="form-card">
                    <div class="form-header">
                        <h1>
                            <i class="bi bi-gear-fill"></i>
                            {{ page_title }}
                        </h1>
                        <div class="subtitle">إنشاء أمر تصنيع جديد بتفاصيل شاملة ومتكاملة</div>
                    </div>

                    <form method="post" id="manufacturing-form">
                        {% csrf_token %}
                        
                        <div class="form-body">
                            <!-- معلومات أساسية -->
                            <div class="section-card">
                                <h3 class="section-title">
                                    <i class="bi bi-info-circle-fill"></i>
                                    المعلومات الأساسية
                                </h3>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="{{ form.order_number.id_for_label }}" class="form-label">{{ form.order_number.label }}</label>
                                        {{ form.order_number }}
                                        {% if form.order_number.errors %}
                                            <div class="alert-danger-custom">{{ form.order_number.errors.0 }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.order_date.id_for_label }}" class="form-label required-field">{{ form.order_date.label }}</label>
                                        {{ form.order_date }}
                                        {% if form.order_date.errors %}
                                            <div class="alert-danger-custom">{{ form.order_date.errors.0 }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.expected_completion_date.id_for_label }}" class="form-label required-field">{{ form.expected_completion_date.label }}</label>
                                        {{ form.expected_completion_date }}
                                        {% if form.expected_completion_date.errors %}
                                            <div class="alert-danger-custom">{{ form.expected_completion_date.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- تفاصيل المنتج -->
                            <div class="section-card">
                                <h3 class="section-title">
                                    <i class="bi bi-box-seam"></i>
                                    تفاصيل المنتج النهائي
                                </h3>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="{{ form.final_product.id_for_label }}" class="form-label required-field">{{ form.final_product.label }}</label>
                                        {{ form.final_product }}
                                        {% if form.final_product.errors %}
                                            <div class="alert-danger-custom">{{ form.final_product.errors.0 }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.quantity_to_produce.id_for_label }}" class="form-label required-field">{{ form.quantity_to_produce.label }}</label>
                                        {{ form.quantity_to_produce }}
                                        {% if form.quantity_to_produce.errors %}
                                            <div class="alert-danger-custom">{{ form.quantity_to_produce.errors.0 }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.unit_of_measure.id_for_label }}" class="form-label required-field">{{ form.unit_of_measure.label }}</label>
                                        {{ form.unit_of_measure }}
                                        {% if form.unit_of_measure.errors %}
                                            <div class="alert-danger-custom">{{ form.unit_of_measure.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- مخزن المنتجات التامة والأولوية -->
                            <div class="section-card">
                                <h3 class="section-title">
                                    <i class="bi bi-building"></i>
                                    مخزن المنتجات التامة والأولوية
                                </h3>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="{{ form.finished_goods_warehouse.id_for_label }}" class="form-label">{{ form.finished_goods_warehouse.label }}</label>
                                        {{ form.finished_goods_warehouse }}
                                        {% if form.finished_goods_warehouse.errors %}
                                            <div class="alert-danger-custom">{{ form.finished_goods_warehouse.errors.0 }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.priority.id_for_label }}" class="form-label">{{ form.priority.label }}</label>
                                        {{ form.priority }}
                                        {% if form.priority.errors %}
                                            <div class="alert-danger-custom">{{ form.priority.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- المواد الخام المطلوبة -->
                            <div class="materials-section">
                                <div class="materials-header">
                                    <h3 class="materials-title">
                                        <i class="bi bi-list-check"></i>
                                        المواد الخام المطلوبة
                                    </h3>
                                    <button type="button" id="add-material-btn" class="add-material-btn">
                                        <i class="bi bi-plus-circle"></i>
                                        إضافة مادة خام
                                    </button>
                                </div>

                                {{ raw_materials_formset.management_form }}

                                <div class="materials-table">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>المخزن</th>
                                                <th>المادة الخام</th>
                                                <th>الكمية المتاحة</th>
                                                <th>الكمية المطلوبة</th>
                                                <th>وحدة القياس</th>
                                                <th>تكلفة الوحدة</th>
                                                <th>إجمالي التكلفة</th>
                                                <th>إجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody class="materials-tbody">
                                            {% for form in raw_materials_formset %}
                                            <tr class="material-row">
                                                <td>
                                                    {{ form.warehouse }}
                                                    {% if form.warehouse.errors %}
                                                        <div class="alert-danger-custom">{{ form.warehouse.errors.0 }}</div>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {{ form.raw_material }}
                                                    {% if form.raw_material.errors %}
                                                        <div class="alert-danger-custom">{{ form.raw_material.errors.0 }}</div>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="available-quantity-display">
                                                        <span class="available-quantity-text text-muted">اختر مادة خام أولاً</span>
                                                        <span class="available-quantity-value d-none">
                                                            <strong class="text-success">0.000</strong>
                                                            <small class="text-muted unit-text"></small>
                                                        </span>
                                                    </div>
                                                </td>
                                                <td>
                                                    {{ form.required_quantity }}
                                                    {% if form.required_quantity.errors %}
                                                        <div class="alert-danger-custom">{{ form.required_quantity.errors.0 }}</div>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {{ form.unit_of_measure }}
                                                    {% if form.unit_of_measure.errors %}
                                                        <div class="alert-danger-custom">{{ form.unit_of_measure.errors.0 }}</div>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {{ form.unit_cost }}
                                                    {% if form.unit_cost.errors %}
                                                        <div class="alert-danger-custom">{{ form.unit_cost.errors.0 }}</div>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="total-cost-display">0.00</span> ج.م
                                                </td>
                                                <td class="text-center">
                                                    <button type="button" class="remove-material-btn" title="حذف">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                    {{ form.DELETE }}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- ملخص التكاليف -->
                                <div class="cost-summary">
                                    <h4><i class="bi bi-calculator"></i> ملخص التكاليف</h4>
                                    <div class="cost-item">
                                        <span>إجمالي تكلفة المواد الخام:</span>
                                        <span id="total-materials-cost">0.00 ج.م</span>
                                    </div>
                                    <div class="cost-item">
                                        <span>تكلفة العمالة المقدرة:</span>
                                        <span id="labor-cost-display">0.00 ج.م</span>
                                    </div>
                                    <div class="cost-item">
                                        <span>التكاليف الإضافية المقدرة:</span>
                                        <span id="overhead-cost-display">0.00 ج.م</span>
                                    </div>
                                    <div class="cost-total">
                                        <div class="cost-item">
                                            <span>إجمالي التكلفة المقدرة:</span>
                                            <span id="total-estimated-cost">0.00 ج.م</span>
                                        </div>
                                        <div class="cost-item" style="margin-top: 10px; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.3);">
                                            <span>الكمية المطلوب إنتاجها:</span>
                                            <span id="production-quantity-display">0</span>
                                        </div>
                                        <div class="cost-item" style="font-size: 1.4rem; font-weight: 800; color: #fff;">
                                            <span>تكلفة الوحدة النهائية:</span>
                                            <span id="unit-cost-display">0.00 ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- التكاليف الإضافية -->
                            <div class="section-card">
                                <h3 class="section-title">
                                    <i class="bi bi-currency-exchange"></i>
                                    التكاليف الإضافية
                                </h3>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="{{ form.estimated_labor_cost.id_for_label }}" class="form-label">{{ form.estimated_labor_cost.label }}</label>
                                        {{ form.estimated_labor_cost }}
                                        {% if form.estimated_labor_cost.errors %}
                                            <div class="alert-danger-custom">{{ form.estimated_labor_cost.errors.0 }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.estimated_overhead_cost.id_for_label }}" class="form-label">{{ form.estimated_overhead_cost.label }}</label>
                                        {{ form.estimated_overhead_cost }}
                                        {% if form.estimated_overhead_cost.errors %}
                                            <div class="alert-danger-custom">{{ form.estimated_overhead_cost.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- ملاحظات ومتطلبات -->
                            <div class="section-card">
                                <h3 class="section-title">
                                    <i class="bi bi-journal-text"></i>
                                    ملاحظات ومتطلبات
                                </h3>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                        {{ form.notes }}
                                        {% if form.notes.errors %}
                                            <div class="alert-danger-custom">{{ form.notes.errors.0 }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.special_instructions.id_for_label }}" class="form-label">{{ form.special_instructions.label }}</label>
                                        {{ form.special_instructions }}
                                        {% if form.special_instructions.errors %}
                                            <div class="alert-danger-custom">{{ form.special_instructions.errors.0 }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="form-group">
                                        <label for="{{ form.quality_requirements.id_for_label }}" class="form-label">{{ form.quality_requirements.label }}</label>
                                        {{ form.quality_requirements }}
                                        {% if form.quality_requirements.errors %}
                                            <div class="alert-danger-custom">{{ form.quality_requirements.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <input type="submit" value="حفظ أمر التصنيع" class="btn btn-primary" />
                            <a href="{% url 'manufacturing:order_list' %}" class="btn btn-secondary">
                                إلغاء والعودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التاريخ والوقت الحالي
    initializeDateTimes();

    // تهيئة حساب التكاليف
    initializeCostCalculations();

    // تهيئة إدارة المواد الخام
    initializeMaterialsManagement();

    // تهيئة حالة المواد الخام (تعطيل المواد إذا لم يتم اختيار مخزن)
    initializeMaterialStates();

    // النموذج جاهز للاستخدام

    console.log('تم تحميل النموذج بنجاح');
});

function initializeDateTimes() {
    const orderDateInput = document.getElementById('id_order_date');
    const completionDateInput = document.getElementById('id_expected_completion_date');

    if (orderDateInput && !orderDateInput.value) {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        orderDateInput.value = localDateTime;
    }

    if (completionDateInput && !completionDateInput.value) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 7); // أسبوع من الآن
        const localDateTime = new Date(tomorrow.getTime() - tomorrow.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        completionDateInput.value = localDateTime;
    }
}

function initializeCostCalculations() {
    // ربط أحداث تغيير التكاليف الإضافية
    const laborCostInput = document.getElementById('id_estimated_labor_cost');
    const overheadCostInput = document.getElementById('id_estimated_overhead_cost');
    const quantityInput = document.getElementById('id_quantity_to_produce');

    if (laborCostInput) {
        laborCostInput.addEventListener('input', updateCostSummary);
    }

    if (overheadCostInput) {
        overheadCostInput.addEventListener('input', updateCostSummary);
    }

    if (quantityInput) {
        quantityInput.addEventListener('input', updateCostSummary);
    }

    // حساب التكاليف الأولي
    updateCostSummary();
}

function initializeMaterialsManagement() {
    // إضافة مادة خام جديدة
    const addMaterialBtn = document.getElementById('add-material-btn');
    if (addMaterialBtn) {
        addMaterialBtn.addEventListener('click', addNewMaterialRow);
    }

    // ربط أحداث المواد الخام الموجودة
    bindMaterialEvents();
}

function addNewMaterialRow() {
    const formsetPrefix = 'raw_materials';
    const totalFormsInput = document.getElementById(`id_${formsetPrefix}-TOTAL_FORMS`);
    const formNum = parseInt(totalFormsInput.value);

    // نسخ آخر صف
    const lastRow = document.querySelector('.material-row:last-child');
    if (!lastRow) {
        console.error('لا يمكن العثور على صف المواد الخام');
        return;
    }

    const newRow = lastRow.cloneNode(true);

    // تحديث الأسماء والمعرفات
    updateFormsetIndexes(newRow, formNum);

    // إعادة تعيين القيم
    resetRowValues(newRow);

    // إضافة الصف الجديد
    document.querySelector('.materials-tbody').appendChild(newRow);

    // تحديث عدد النماذج
    totalFormsInput.value = formNum + 1;

    // ربط الأحداث للصف الجديد
    bindRowEvents(newRow);

    // إعادة حساب التكاليف
    updateCostSummary();
}

function updateFormsetIndexes(row, newIndex) {
    row.querySelectorAll('input, select, textarea').forEach(function(element) {
        if (element.name) {
            element.name = element.name.replace(/-\d+-/, `-${newIndex}-`);
        }
        if (element.id) {
            element.id = element.id.replace(/-\d+-/, `-${newIndex}-`);
        }
    });
}

function resetRowValues(row) {
    row.querySelectorAll('input, select, textarea').forEach(function(element) {
        if (element.type === 'checkbox') {
            element.checked = false;
        } else if (element.type !== 'hidden') {
            element.value = '';
        }
    });

    // إعادة تعيين خانة المادة الخام في الصف الجديد
    const materialSelect = row.querySelector('.material-select');
    if (materialSelect) {
        resetMaterialOptions(materialSelect);
    }

    // إعادة تعيين عرض التكلفة الإجمالية
    const totalCostDisplay = row.querySelector('.total-cost-display');
    if (totalCostDisplay) {
        totalCostDisplay.textContent = '0.00';
    }
}

function bindMaterialEvents() {
    document.querySelectorAll('.material-row').forEach(bindRowEvents);
}

function bindRowEvents(row) {
    // حدث تغيير المادة الخام
    const materialSelect = row.querySelector('.material-select');
    if (materialSelect) {
        materialSelect.addEventListener('change', function() {
            handleMaterialChange(this, row);
        });
    }

    // حدث تغيير المخزن
    const warehouseSelect = row.querySelector('.warehouse-select');
    if (warehouseSelect) {
        warehouseSelect.addEventListener('change', function() {
            console.log('تم تغيير المخزن:', this.value);
            handleWarehouseChange(this, row);
        });
    }

    // حدث تغيير الكمية
    const quantityInput = row.querySelector('.quantity-input');
    if (quantityInput) {
        quantityInput.addEventListener('input', function() {
            calculateRowTotal(row);
        });
    }

    // حدث تغيير تكلفة الوحدة
    const unitCostInput = row.querySelector('.unit-cost-input');
    if (unitCostInput) {
        unitCostInput.addEventListener('input', function() {
            calculateRowTotal(row);
        });
    }

    // حدث حذف الصف
    const removeBtn = row.querySelector('.remove-material-btn');
    if (removeBtn) {
        removeBtn.addEventListener('click', function() {
            removeMaterialRow(row);
        });
    }
}

function handleWarehouseChange(selectElement, row) {
    const warehouseId = selectElement.value;
    const materialSelect = row.querySelector('.material-select');

    if (!warehouseId) {
        // إعادة تعيين قائمة المواد الخام لتشمل جميع المواد
        resetMaterialOptions(materialSelect);
        return;
    }

    // إظهار حالة التحميل للمواد الخام
    showMaterialLoadingState(materialSelect);

    // جلب المواد الخام المتوفرة في المخزن المحدد
    fetch(`/manufacturing/api/warehouse-materials/${warehouseId}/`, {
        method: 'GET',
        headers: {
            'X-CSRFToken': getCSRFToken(),
            'Content-Type': 'application/json',
        },
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            updateMaterialOptions(materialSelect, data.materials);

            // تحديث الكمية المتاحة إذا كانت هناك مادة مختارة
            const selectedMaterialId = materialSelect.value;
            if (selectedMaterialId) {
                updateAvailableQuantityFromWarehouse(row, selectedMaterialId, warehouseId);
            } else {
                resetAvailableQuantity(row);
            }
        } else {
            showMaterialErrorState(materialSelect, data.error || 'فشل في جلب مواد المخزن');
            resetAvailableQuantity(row);
        }
    })
    .catch(error => {
        console.error('خطأ في جلب مواد المخزن:', error);
        showMaterialErrorState(materialSelect, 'خطأ في الاتصال بالخادم');
    });
}

function handleMaterialChange(selectElement, row) {
    const materialId = selectElement.value;

    if (!materialId) {
        resetRowCosts(row);
        return;
    }

    // إظهار حالة التحميل
    showLoadingState(row);

    // جلب بيانات المادة الخام
    fetch(`/manufacturing/api/product-cost/${materialId}/`, {
        method: 'GET',
        headers: {
            'X-CSRFToken': getCSRFToken(),
            'Content-Type': 'application/json',
        },
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            updateRowFromAPI(row, data);
        } else {
            showErrorState(row, data.error || 'فشل في جلب بيانات المادة الخام');
        }
    })
    .catch(error => {
        console.error('خطأ في جلب بيانات المادة الخام:', error);
        showErrorState(row, 'خطأ في الاتصال بالخادم');
    });
}

function showLoadingState(row) {
    const unitCostInput = row.querySelector('.unit-cost-input');
    const unitSelect = row.querySelector('.unit-select');

    if (unitCostInput) {
        unitCostInput.classList.add('loading-input');
        unitCostInput.placeholder = 'جاري التحميل...';
        unitCostInput.disabled = true;
    }

    if (unitSelect) {
        unitSelect.classList.add('loading-input');
        unitSelect.disabled = true;
    }
}

function updateRowFromAPI(row, data) {
    const unitCostInput = row.querySelector('.unit-cost-input');
    const unitSelect = row.querySelector('.unit-select');

    // تحديث تكلفة الوحدة
    if (unitCostInput) {
        unitCostInput.classList.remove('loading-input');
        unitCostInput.classList.add('success-input');
        unitCostInput.value = parseFloat(data.cost_price || 0).toFixed(2);
        unitCostInput.placeholder = data.has_cost_price ? 'تم جلبه من التعريفات' : 'لا يوجد سعر محدد';
        unitCostInput.disabled = false;

        // إزالة تنسيق النجاح بعد ثانيتين
        setTimeout(() => {
            unitCostInput.classList.remove('success-input');
            unitCostInput.placeholder = '0.00';
        }, 2000);
    }

    // تحديث وحدة القياس إذا كانت متاحة
    if (unitSelect && data.main_unit_value) {
        // البحث عن الخيار المطابق
        const options = unitSelect.querySelectorAll('option');
        for (let option of options) {
            if (option.textContent.includes(data.main_unit_display)) {
                unitSelect.classList.remove('loading-input');
                unitSelect.classList.add('success-input');
                unitSelect.value = option.value;
                unitSelect.disabled = false;

                setTimeout(() => {
                    unitSelect.classList.remove('success-input');
                }, 2000);
                break;
            }
        }
    } else if (unitSelect) {
        unitSelect.classList.remove('loading-input');
        unitSelect.disabled = false;
    }

    // تحديث الكمية المتاحة إذا كان هناك مخزن مختار
    const warehouseSelect = row.querySelector('.warehouse-select');
    const materialSelect = row.querySelector('.material-select');

    if (warehouseSelect && warehouseSelect.value && materialSelect && materialSelect.value) {
        updateAvailableQuantityFromWarehouse(row, materialSelect.value, warehouseSelect.value);
    } else {
        showAvailableQuantityMessage(row, 'اختر مخزناً أولاً');
    }

    // إعادة حساب التكلفة الإجمالية
    calculateRowTotal(row);
}

// دوال إدارة الكمية المتاحة
function showAvailableQuantityLoading(row) {
    const availableDisplay = row.querySelector('.available-quantity-display');
    if (availableDisplay) {
        availableDisplay.innerHTML = '<span class="text-muted"><i class="bi bi-hourglass-split me-1"></i>جاري التحميل...</span>';
    }
}

function showAvailableQuantityMessage(row, message) {
    const availableDisplay = row.querySelector('.available-quantity-display');
    if (availableDisplay) {
        availableDisplay.innerHTML = `<span class="text-muted">${message}</span>`;
    }
}

function updateAvailableQuantity(row, quantity, unit) {
    const availableDisplay = row.querySelector('.available-quantity-display');
    if (availableDisplay) {
        const quantityClass = quantity > 0 ? 'text-success' : 'text-warning';
        const quantityText = quantity > 0 ? quantity.toFixed(3) : '0.000';
        availableDisplay.innerHTML = `
            <strong class="${quantityClass}">${quantityText}</strong>
            <small class="text-muted">${unit}</small>
        `;
    }
}

function resetAvailableQuantity(row) {
    showAvailableQuantityMessage(row, 'اختر مادة خام أولاً');
}

function updateAvailableQuantityFromWarehouse(row, materialId, warehouseId) {
    showAvailableQuantityLoading(row);

    fetch(`/manufacturing/api/warehouse-materials/${warehouseId}/`, {
        method: 'GET',
        headers: {
            'X-CSRFToken': getCSRFToken(),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const material = data.materials.find(m => m.id == materialId);
            if (material) {
                updateAvailableQuantity(row, material.available_quantity, material.unit_name);
            } else {
                updateAvailableQuantity(row, 0, '');
            }
        } else {
            showAvailableQuantityMessage(row, 'خطأ في جلب البيانات');
        }
    })
    .catch(error => {
        console.error('خطأ في جلب الكمية المتاحة:', error);
        showAvailableQuantityMessage(row, 'خطأ في الاتصال');
    });
}

function showErrorState(row, errorMessage) {
    const unitCostInput = row.querySelector('.unit-cost-input');
    const unitSelect = row.querySelector('.unit-select');

    if (unitCostInput) {
        unitCostInput.classList.remove('loading-input');
        unitCostInput.value = '0';
        unitCostInput.placeholder = errorMessage;
        unitCostInput.style.backgroundColor = '#fff3cd';
        unitCostInput.disabled = false;
    }

    if (unitSelect) {
        unitSelect.classList.remove('loading-input');
        unitSelect.disabled = false;
    }
}

function resetRowCosts(row) {
    const unitCostInput = row.querySelector('.unit-cost-input');
    const totalCostDisplay = row.querySelector('.total-cost-display');

    if (unitCostInput) {
        unitCostInput.value = '0';
        unitCostInput.placeholder = '0.00';
        unitCostInput.style.backgroundColor = '';
    }

    if (totalCostDisplay) {
        totalCostDisplay.textContent = '0.00';
    }

    // إعادة تعيين الكمية المتاحة
    resetAvailableQuantity(row);

    updateCostSummary();
}

function calculateRowTotal(row) {
    const quantityInput = row.querySelector('.quantity-input');
    const unitCostInput = row.querySelector('.unit-cost-input');
    const totalCostDisplay = row.querySelector('.total-cost-display');

    if (!quantityInput || !unitCostInput || !totalCostDisplay) return;

    const quantity = parseFloat(quantityInput.value) || 0;
    const unitCost = parseFloat(unitCostInput.value) || 0;
    const total = quantity * unitCost;

    totalCostDisplay.textContent = total.toFixed(2);

    // تحديث ملخص التكاليف
    updateCostSummary();
}

function removeMaterialRow(row) {
    const deleteCheckbox = row.querySelector('input[name$="-DELETE"]');

    if (deleteCheckbox) {
        deleteCheckbox.checked = true;
        row.style.display = 'none';
    } else {
        row.remove();
    }

    updateCostSummary();
}

function updateCostSummary() {
    let totalMaterialsCost = 0;

    // حساب إجمالي تكلفة المواد الخام
    document.querySelectorAll('.material-row:not([style*="display: none"]) .total-cost-display').forEach(function(element) {
        const cost = parseFloat(element.textContent) || 0;
        totalMaterialsCost += cost;
    });

    // الحصول على التكاليف الإضافية
    const laborCost = parseFloat(document.getElementById('id_estimated_labor_cost')?.value) || 0;
    const overheadCost = parseFloat(document.getElementById('id_estimated_overhead_cost')?.value) || 0;

    // الحصول على الكمية المطلوب إنتاجها
    const productionQuantity = parseFloat(document.getElementById('id_quantity_to_produce')?.value) || 0;

    // حساب التكلفة الإجمالية
    const totalEstimatedCost = totalMaterialsCost + laborCost + overheadCost;

    // حساب تكلفة الوحدة النهائية
    const unitCost = productionQuantity > 0 ? totalEstimatedCost / productionQuantity : 0;

    // تحديث العرض
    document.getElementById('total-materials-cost').textContent = totalMaterialsCost.toFixed(2) + ' ج.م';
    document.getElementById('labor-cost-display').textContent = laborCost.toFixed(2) + ' ج.م';
    document.getElementById('overhead-cost-display').textContent = overheadCost.toFixed(2) + ' ج.م';
    document.getElementById('total-estimated-cost').textContent = totalEstimatedCost.toFixed(2) + ' ج.م';
    document.getElementById('production-quantity-display').textContent = productionQuantity.toFixed(3);
    document.getElementById('unit-cost-display').textContent = unitCost.toFixed(2) + ' ج.م';
}

// دوال مساعدة للتعامل مع قائمة المواد الخام
function showMaterialLoadingState(materialSelect) {
    materialSelect.innerHTML = '<option value="">جاري تحميل المواد الخام...</option>';
    materialSelect.disabled = true;
    materialSelect.classList.add('loading-input');
}

function updateMaterialOptions(materialSelect, materials) {
    materialSelect.classList.remove('loading-input');
    materialSelect.disabled = false;

    // مسح الخيارات الحالية
    materialSelect.innerHTML = '<option value="">اختر المادة الخام</option>';

    // إضافة المواد الجديدة
    materials.forEach(function(material) {
        const option = document.createElement('option');
        option.value = material.id;
        option.textContent = material.display_text;
        option.dataset.availableQuantity = material.available_quantity;
        option.dataset.costPrice = material.cost_price;
        materialSelect.appendChild(option);
    });

    console.log(`تم تحديث قائمة المواد الخام: ${materials.length} مادة`);
}

function showMaterialErrorState(materialSelect, errorMessage) {
    materialSelect.classList.remove('loading-input');
    materialSelect.disabled = false;
    materialSelect.innerHTML = `<option value="">خطأ: ${errorMessage}</option>`;
}

function resetMaterialOptions(materialSelect) {
    // إعادة تعيين إلى حالة فارغة - يجب اختيار مخزن أولاً
    materialSelect.classList.remove('loading-input');
    materialSelect.disabled = false;
    materialSelect.value = '';
    materialSelect.innerHTML = '<option value="">اختر مخزناً أولاً</option>';
}

function disableMaterialSelect(materialSelect) {
    materialSelect.classList.remove('loading-input');
    materialSelect.innerHTML = '<option value="">اختر مخزناً أولاً</option>';
    materialSelect.disabled = false;
    materialSelect.value = '';
}

function initializeMaterialStates() {
    // تحقق من جميع صفوف المواد الخام الموجودة
    document.querySelectorAll('.material-row').forEach(function(row) {
        const warehouseSelect = row.querySelector('.warehouse-select');
        const materialSelect = row.querySelector('.material-select');

        if (warehouseSelect && materialSelect) {
            // إذا لم يتم اختيار مخزن، قم بإعادة تعيين المادة الخام
            if (!warehouseSelect.value) {
                resetMaterialOptions(materialSelect);
            }
        }
    });
}

function getCSRFToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value;
}

// تهيئة الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // ربط أحداث حذف المواد الخام
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-material-btn')) {
            const row = e.target.closest('.material-row');
            removeMaterialRow(row);
        }
    });

    // ربط أحداث تغيير المواد الخام
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('material-select')) {
            const row = e.target.closest('.material-row');
            handleMaterialChange(e.target, row);
        }
    });

    // ربط أحداث تغيير الكمية وتكلفة الوحدة
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('quantity-input') || e.target.classList.contains('unit-cost-input')) {
            const row = e.target.closest('.material-row');
            calculateRowTotal(row);
        }
    });
});
</script>
{% endblock %}
