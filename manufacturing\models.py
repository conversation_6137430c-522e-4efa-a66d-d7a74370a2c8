from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
from django.utils import timezone
from definitions.models import ProductDefinition, WarehouseDefinition, UnitDefinition


class ManufacturingOrder(models.Model):
    """أمر التصنيع الرئيسي"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('approved', 'معتمد'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
        ('on_hold', 'معلق'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة'),
    ]

    # معلومات أساسية
    order_number = models.CharField(max_length=50, unique=True, blank=True, verbose_name="رقم أمر التصنيع")
    order_date = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الأمر")
    expected_completion_date = models.DateTimeField(verbose_name="تاريخ الإنجاز المتوقع")
    actual_completion_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الإنجاز الفعلي")

    # المنتج المراد تصنيعه
    final_product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE,
                                    related_name='manufacturing_orders', verbose_name="المنتج النهائي")
    quantity_to_produce = models.DecimalField(max_digits=12, decimal_places=3,
                                            validators=[MinValueValidator(Decimal('0.001'))],
                                            verbose_name="الكمية المطلوب إنتاجها")
    unit_of_measure = models.ForeignKey(UnitDefinition, on_delete=models.CASCADE, verbose_name="وحدة القياس")

    # المخازن
    raw_materials_warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE,
                                              related_name='raw_material_orders', verbose_name="مخزن المواد الخام",
                                              null=True, blank=True)
    finished_goods_warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE,
                                                related_name='finished_goods_orders', verbose_name="مخزن المنتجات التامة")

    # الحالة والأولوية
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='normal', verbose_name="الأولوية")

    # التكاليف
    estimated_raw_material_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="تكلفة المواد الخام المقدرة")
    estimated_labor_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="تكلفة العمالة المقدرة")
    estimated_overhead_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="التكاليف الإضافية المقدرة")
    actual_raw_material_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="تكلفة المواد الخام الفعلية")
    actual_labor_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="تكلفة العمالة الفعلية")
    actual_overhead_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="التكاليف الإضافية الفعلية")

    # معلومات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    special_instructions = models.TextField(blank=True, verbose_name="تعليمات خاصة")
    quality_requirements = models.TextField(blank=True, verbose_name="متطلبات الجودة")

    # معلومات الموافقة
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='approved_manufacturing_orders', verbose_name="معتمد بواسطة")
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    # معلومات التنفيذ
    started_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                 related_name='started_manufacturing_orders', verbose_name="بدأ التنفيذ بواسطة")
    started_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ بدء التنفيذ")
    completed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='completed_manufacturing_orders', verbose_name="أكمل بواسطة")

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "أمر تصنيع"
        verbose_name_plural = "أوامر التصنيع"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order_number']),
            models.Index(fields=['status']),
            models.Index(fields=['order_date']),
        ]

    def __str__(self):
        return f"{self.order_number} - {self.final_product.name}"

    @property
    def total_estimated_cost(self):
        """إجمالي التكلفة المقدرة"""
        return self.estimated_raw_material_cost + self.estimated_labor_cost + self.estimated_overhead_cost

    @property
    def total_actual_cost(self):
        """إجمالي التكلفة الفعلية"""
        return self.actual_raw_material_cost + self.actual_labor_cost + self.actual_overhead_cost

    @property
    def cost_variance(self):
        """انحراف التكلفة"""
        return self.total_actual_cost - self.total_estimated_cost

    @property
    def cost_variance_percentage(self):
        """نسبة انحراف التكلفة"""
        if self.total_estimated_cost > 0:
            return (self.cost_variance / self.total_estimated_cost) * 100
        return 0

    @property
    def is_overdue(self):
        """هل الأمر متأخر"""
        if self.status not in ['completed', 'cancelled']:
            return timezone.now() > self.expected_completion_date
        return False

    @property
    def progress_percentage(self):
        """نسبة الإنجاز"""
        if self.status == 'completed':
            return 100
        elif self.status == 'in_progress':
            # حساب النسبة بناءً على المراحل المكتملة
            total_stages = self.production_stages.count()
            completed_stages = self.production_stages.filter(status='completed').count()
            if total_stages > 0:
                return (completed_stages / total_stages) * 100
        return 0


class ManufacturingOrderRawMaterial(models.Model):
    """المواد الخام المطلوبة لأمر التصنيع"""
    manufacturing_order = models.ForeignKey(ManufacturingOrder, on_delete=models.CASCADE,
                                          related_name='raw_materials', verbose_name="أمر التصنيع")
    raw_material = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE,
                                   limit_choices_to={'product_type': 'raw_material'},
                                   verbose_name="المادة الخام")
    warehouse = models.ForeignKey('definitions.WarehouseDefinition', on_delete=models.CASCADE,
                                verbose_name="المخزن", null=True, blank=True)

    # الكميات
    required_quantity = models.DecimalField(max_digits=12, decimal_places=3,
                                          validators=[MinValueValidator(Decimal('0.001'))],
                                          verbose_name="الكمية المطلوبة")
    allocated_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0,
                                           verbose_name="الكمية المخصصة")
    consumed_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0,
                                          verbose_name="الكمية المستهلكة")
    unit_of_measure = models.ForeignKey(UnitDefinition, on_delete=models.CASCADE, verbose_name="وحدة القياس")

    # التكاليف
    unit_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي التكلفة")

    # معلومات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    is_critical = models.BooleanField(default=False, verbose_name="مادة حرجة")
    substitute_materials = models.ManyToManyField(ProductDefinition, blank=True,
                                                related_name='substitute_for',
                                                verbose_name="المواد البديلة")

    # حالة التوفر
    is_available = models.BooleanField(default=False, verbose_name="متوفرة")
    shortage_quantity = models.DecimalField(max_digits=12, decimal_places=3, default=0,
                                          verbose_name="كمية النقص")

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مادة خام لأمر التصنيع"
        verbose_name_plural = "المواد الخام لأوامر التصنيع"
        unique_together = ['manufacturing_order', 'raw_material']
        ordering = ['raw_material__name']

    def __str__(self):
        return f"{self.manufacturing_order.order_number} - {self.raw_material.name}"

    def save(self, *args, **kwargs):
        # حساب إجمالي التكلفة تلقائياً
        self.total_cost = self.required_quantity * self.unit_cost

        # تحديث تكلفة الوحدة من تعريف المنتج إذا لم تكن محددة
        if self.unit_cost == 0 and self.raw_material.cost_price:
            self.unit_cost = self.raw_material.cost_price
            self.total_cost = self.required_quantity * self.unit_cost

        super().save(*args, **kwargs)

        # تحديث إجمالي تكلفة المواد الخام في أمر التصنيع
        self.update_manufacturing_order_cost()

    def update_manufacturing_order_cost(self):
        """تحديث تكلفة المواد الخام في أمر التصنيع"""
        total_raw_material_cost = self.manufacturing_order.raw_materials.aggregate(
            total=models.Sum('total_cost')
        )['total'] or 0

        self.manufacturing_order.estimated_raw_material_cost = total_raw_material_cost
        self.manufacturing_order.save(update_fields=['estimated_raw_material_cost'])

    @property
    def remaining_quantity(self):
        """الكمية المتبقية"""
        return self.required_quantity - self.consumed_quantity

    @property
    def allocation_percentage(self):
        """نسبة التخصيص"""
        if self.required_quantity > 0:
            return (self.allocated_quantity / self.required_quantity) * 100
        return 0

    @property
    def consumption_percentage(self):
        """نسبة الاستهلاك"""
        if self.required_quantity > 0:
            return (self.consumed_quantity / self.required_quantity) * 100
        return 0


class ManufacturingOrderStage(models.Model):
    """مراحل تنفيذ أمر التصنيع"""
    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('on_hold', 'معلق'),
        ('cancelled', 'ملغي'),
    ]

    manufacturing_order = models.ForeignKey(ManufacturingOrder, on_delete=models.CASCADE,
                                          related_name='production_stages', verbose_name="أمر التصنيع")
    stage_name = models.CharField(max_length=100, verbose_name="اسم المرحلة")
    stage_description = models.TextField(blank=True, verbose_name="وصف المرحلة")
    sequence_number = models.IntegerField(verbose_name="رقم التسلسل")

    # التوقيتات
    planned_start_date = models.DateTimeField(verbose_name="تاريخ البدء المخطط")
    planned_end_date = models.DateTimeField(verbose_name="تاريخ الانتهاء المخطط")
    actual_start_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ البدء الفعلي")
    actual_end_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الانتهاء الفعلي")

    # الحالة والتقدم
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="الحالة")
    progress_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0,
                                            validators=[MinValueValidator(0), MinValueValidator(100)],
                                            verbose_name="نسبة الإنجاز")

    # التكاليف والموارد
    estimated_labor_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0, verbose_name="ساعات العمل المقدرة")
    actual_labor_hours = models.DecimalField(max_digits=8, decimal_places=2, default=0, verbose_name="ساعات العمل الفعلية")
    labor_cost_per_hour = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="تكلفة العمالة/ساعة")
    overhead_cost = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="التكاليف الإضافية")

    # الجودة
    quality_check_required = models.BooleanField(default=False, verbose_name="يتطلب فحص جودة")
    quality_check_passed = models.BooleanField(default=False, verbose_name="اجتاز فحص الجودة")
    quality_notes = models.TextField(blank=True, verbose_name="ملاحظات الجودة")

    # المسؤولين
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='assigned_manufacturing_stages', verbose_name="مسند إلى")
    completed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='completed_manufacturing_stages', verbose_name="أكمل بواسطة")

    # معلومات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    equipment_used = models.TextField(blank=True, verbose_name="المعدات المستخدمة")

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مرحلة أمر التصنيع"
        verbose_name_plural = "مراحل أوامر التصنيع"
        ordering = ['sequence_number']
        unique_together = ['manufacturing_order', 'sequence_number']

    def __str__(self):
        return f"{self.manufacturing_order.order_number} - {self.stage_name}"

    @property
    def estimated_labor_cost(self):
        """تكلفة العمالة المقدرة"""
        return self.estimated_labor_hours * self.labor_cost_per_hour

    @property
    def actual_labor_cost(self):
        """تكلفة العمالة الفعلية"""
        return self.actual_labor_hours * self.labor_cost_per_hour

    @property
    def total_estimated_cost(self):
        """إجمالي التكلفة المقدرة للمرحلة"""
        return self.estimated_labor_cost + self.overhead_cost

    @property
    def total_actual_cost(self):
        """إجمالي التكلفة الفعلية للمرحلة"""
        return self.actual_labor_cost + self.overhead_cost

    @property
    def is_overdue(self):
        """هل المرحلة متأخرة"""
        if self.status not in ['completed', 'cancelled']:
            return timezone.now() > self.planned_end_date
        return False

    @property
    def duration_planned(self):
        """المدة المخططة بالساعات"""
        if self.planned_end_date and self.planned_start_date:
            delta = self.planned_end_date - self.planned_start_date
            return delta.total_seconds() / 3600
        return 0

    @property
    def duration_actual(self):
        """المدة الفعلية بالساعات"""
        if self.actual_end_date and self.actual_start_date:
            delta = self.actual_end_date - self.actual_start_date
            return delta.total_seconds() / 3600
        return 0


class ManufacturingInventoryTransaction(models.Model):
    """حركات المخزون للتصنيع"""
    TRANSACTION_TYPES = [
        ('raw_material_consumption', 'استهلاك مواد خام'),
        ('finished_goods_production', 'إنتاج منتجات تامة'),
        ('waste_disposal', 'التخلص من المخلفات'),
        ('return_to_stock', 'إرجاع للمخزون'),
        ('adjustment', 'تسوية'),
    ]

    manufacturing_order = models.ForeignKey(ManufacturingOrder, on_delete=models.CASCADE,
                                          related_name='inventory_transactions', verbose_name="أمر التصنيع")
    transaction_type = models.CharField(max_length=30, choices=TRANSACTION_TYPES, verbose_name="نوع الحركة")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="المنتج")
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, verbose_name="المخزن")

    # الكميات
    quantity = models.DecimalField(max_digits=12, decimal_places=3, verbose_name="الكمية")
    unit_of_measure = models.ForeignKey(UnitDefinition, on_delete=models.CASCADE, verbose_name="وحدة القياس")
    unit_cost = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="إجمالي التكلفة")

    # معلومات الحركة
    transaction_date = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الحركة")
    reference_number = models.CharField(max_length=50, blank=True, verbose_name="رقم المرجع")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # حالة الحركة
    is_processed = models.BooleanField(default=False, verbose_name="تم المعالجة")
    processed_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ المعالجة")
    processed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='processed_manufacturing_transactions', verbose_name="معالج بواسطة")

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "حركة مخزون التصنيع"
        verbose_name_plural = "حركات مخزون التصنيع"
        ordering = ['-transaction_date']
        indexes = [
            models.Index(fields=['transaction_date']),
            models.Index(fields=['transaction_type']),
            models.Index(fields=['is_processed']),
        ]

    def __str__(self):
        return f"{self.get_transaction_type_display()} - {self.product.name} - {self.quantity}"

    def save(self, *args, **kwargs):
        # حساب إجمالي التكلفة
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)


class ManufacturingQualityCheck(models.Model):
    """فحص الجودة للتصنيع"""
    CHECK_TYPES = [
        ('incoming_materials', 'فحص المواد الواردة'),
        ('in_process', 'فحص أثناء الإنتاج'),
        ('final_inspection', 'الفحص النهائي'),
        ('random_sampling', 'عينة عشوائية'),
    ]

    RESULT_CHOICES = [
        ('passed', 'مقبول'),
        ('failed', 'مرفوض'),
        ('conditional', 'مقبول بشروط'),
        ('pending', 'قيد الفحص'),
    ]

    manufacturing_order = models.ForeignKey(ManufacturingOrder, on_delete=models.CASCADE,
                                          related_name='quality_checks', verbose_name="أمر التصنيع")
    stage = models.ForeignKey(ManufacturingOrderStage, on_delete=models.CASCADE, null=True, blank=True,
                            related_name='quality_checks', verbose_name="المرحلة")

    check_type = models.CharField(max_length=20, choices=CHECK_TYPES, verbose_name="نوع الفحص")
    check_date = models.DateTimeField(default=timezone.now, verbose_name="تاريخ الفحص")
    inspector = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المفتش")

    # نتائج الفحص
    result = models.CharField(max_length=15, choices=RESULT_CHOICES, default='pending', verbose_name="النتيجة")
    score = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True,
                              validators=[MinValueValidator(0), MinValueValidator(100)],
                              verbose_name="النقاط")

    # التفاصيل
    criteria_checked = models.TextField(verbose_name="المعايير المفحوصة")
    defects_found = models.TextField(blank=True, verbose_name="العيوب المكتشفة")
    corrective_actions = models.TextField(blank=True, verbose_name="الإجراءات التصحيحية")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # المرفقات
    test_results_file = models.FileField(upload_to='manufacturing/quality_checks/', null=True, blank=True,
                                       verbose_name="ملف نتائج الاختبار")
    photos = models.TextField(blank=True, verbose_name="الصور")  # JSON field for multiple photos

    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "فحص جودة التصنيع"
        verbose_name_plural = "فحوصات جودة التصنيع"
        ordering = ['-check_date']

    def __str__(self):
        return f"{self.manufacturing_order.order_number} - {self.get_check_type_display()} - {self.get_result_display()}"
