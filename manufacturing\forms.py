from django import forms
from django.core.exceptions import ValidationError
from django.forms import inlineformset_factory
from decimal import Decimal
from .models import (
    ManufacturingOrder, ManufacturingOrderRawMaterial, ManufacturingOrderStage,
    ManufacturingInventoryTransaction, ManufacturingQualityCheck
)
from definitions.models import ProductDefinition, WarehouseDefinition, UnitDefinition


class ManufacturingOrderForm(forms.ModelForm):
    class Meta:
        model = ManufacturingOrder
        fields = [
            'order_number', 'order_date', 'expected_completion_date', 'final_product',
            'quantity_to_produce', 'unit_of_measure', 'raw_materials_warehouse',
            'finished_goods_warehouse', 'priority', 'estimated_labor_cost',
            'estimated_overhead_cost', 'notes', 'special_instructions', 'quality_requirements'
        ]
        
        widgets = {
            'order_number': forms.TextInput(attrs={
                'class': 'form-control form-control-lg',
                'placeholder': 'سيتم إنشاؤه تلقائياً إذا ترك فارغاً'
            }),
            'order_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'expected_completion_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'final_product': forms.Select(attrs={
                'class': 'form-select form-select-lg',
                'data-live-search': 'true'
            }),
            'quantity_to_produce': forms.NumberInput(attrs={
                'class': 'form-control form-control-lg',
                'step': '0.001',
                'min': '0.001'
            }),
            'unit_of_measure': forms.Select(attrs={
                'class': 'form-select'
            }),
            'raw_materials_warehouse': forms.Select(attrs={
                'class': 'form-select form-select-lg'
            }),
            'finished_goods_warehouse': forms.Select(attrs={
                'class': 'form-select form-select-lg'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select'
            }),
            'estimated_labor_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'estimated_overhead_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات عامة حول أمر التصنيع'
            }),
            'special_instructions': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'تعليمات خاصة للإنتاج'
            }),
            'quality_requirements': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'متطلبات الجودة والمواصفات'
            }),
        }
        
        labels = {
            'order_number': 'رقم أمر التصنيع',
            'order_date': 'تاريخ الأمر',
            'expected_completion_date': 'تاريخ الإنجاز المتوقع',
            'final_product': 'المنتج النهائي',
            'quantity_to_produce': 'الكمية المطلوب إنتاجها',
            'unit_of_measure': 'وحدة القياس',
            'raw_materials_warehouse': 'مخزن المواد الخام',
            'finished_goods_warehouse': 'مخزن المنتجات التامة',
            'priority': 'الأولوية',
            'estimated_labor_cost': 'تكلفة العمالة المقدرة',
            'estimated_overhead_cost': 'التكاليف الإضافية المقدرة',
            'notes': 'ملاحظات',
            'special_instructions': 'تعليمات خاصة',
            'quality_requirements': 'متطلبات الجودة',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تحديد الخيارات للحقول المرتبطة
        self.fields['final_product'].queryset = ProductDefinition.objects.filter(
            is_active=True, product_type__in=['finished_good', 'semi_finished']
        ).order_by('name')

        # عرض جميع المخازن النشطة مرتبة حسب النوع والاسم
        all_warehouses = WarehouseDefinition.objects.filter(is_active=True).order_by('warehouse_type', 'name')

        # مخزن المواد الخام - عرض جميع المخازن ما عدا التالفة والحجر الصحي
        raw_materials_queryset = all_warehouses.exclude(warehouse_type__in=['damaged', 'quarantine'])
        self.fields['raw_materials_warehouse'].queryset = raw_materials_queryset

        # مخزن المنتجات التامة - عرض جميع المخازن ما عدا التالفة والحجر الصحي
        finished_goods_queryset = all_warehouses.exclude(warehouse_type__in=['damaged', 'quarantine'])
        self.fields['finished_goods_warehouse'].queryset = finished_goods_queryset

        # تخصيص عرض المخازن مع أنواعها
        self._customize_warehouse_choices()

        self.fields['unit_of_measure'].queryset = UnitDefinition.objects.filter(is_active=True).order_by('name')

    def _customize_warehouse_choices(self):
        """تخصيص عرض المخازن مع أنواعها"""
        # تخصيص خيارات مخزن المواد الخام
        raw_choices = []
        for warehouse in self.fields['raw_materials_warehouse'].queryset:
            label = f"{warehouse.name} ({warehouse.get_warehouse_type_display()})"
            raw_choices.append((warehouse.pk, label))
        self.fields['raw_materials_warehouse'].choices = [('', '---------')] + raw_choices

        # تخصيص خيارات مخزن المنتجات التامة
        finished_choices = []
        for warehouse in self.fields['finished_goods_warehouse'].queryset:
            label = f"{warehouse.name} ({warehouse.get_warehouse_type_display()})"
            finished_choices.append((warehouse.pk, label))
        self.fields['finished_goods_warehouse'].choices = [('', '---------')] + finished_choices
        
        # جعل الحقول المطلوبة واضحة
        required_fields = ['final_product', 'quantity_to_produce', 'unit_of_measure', 
                          'raw_materials_warehouse', 'finished_goods_warehouse', 'expected_completion_date']
        for field_name in required_fields:
            self.fields[field_name].required = True
    
    def clean(self):
        cleaned_data = super().clean()
        order_date = cleaned_data.get('order_date')
        expected_completion_date = cleaned_data.get('expected_completion_date')
        quantity_to_produce = cleaned_data.get('quantity_to_produce')
        raw_materials_warehouse = cleaned_data.get('raw_materials_warehouse')
        finished_goods_warehouse = cleaned_data.get('finished_goods_warehouse')
        
        # التحقق من أن تاريخ الإنجاز بعد تاريخ الأمر
        if order_date and expected_completion_date and expected_completion_date <= order_date:
            raise ValidationError('تاريخ الإنجاز المتوقع يجب أن يكون بعد تاريخ الأمر')
        
        # التحقق من أن الكمية أكبر من صفر
        if quantity_to_produce and quantity_to_produce <= 0:
            raise ValidationError('الكمية المطلوب إنتاجها يجب أن تكون أكبر من صفر')
        
        # التحقق من أن مخزن المواد الخام مختلف عن مخزن المنتجات التامة
        if raw_materials_warehouse and finished_goods_warehouse and raw_materials_warehouse == finished_goods_warehouse:
            raise ValidationError('مخزن المواد الخام يجب أن يكون مختلفاً عن مخزن المنتجات التامة')
        
        return cleaned_data
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # إنشاء رقم أمر التصنيع تلقائياً إذا لم يكن محدداً
        if not instance.order_number:
            from django.utils import timezone
            today = timezone.now().strftime('%Y%m%d')
            last_order = ManufacturingOrder.objects.filter(
                order_number__startswith=f'MO{today}'
            ).order_by('-order_number').first()
            
            if last_order:
                last_number = int(last_order.order_number[-4:])
                new_number = last_number + 1
            else:
                new_number = 1
            
            instance.order_number = f'MO{today}{new_number:04d}'
        
        if commit:
            instance.save()
        return instance


class ManufacturingOrderRawMaterialForm(forms.ModelForm):
    class Meta:
        model = ManufacturingOrderRawMaterial
        fields = [
            'raw_material', 'required_quantity', 'unit_of_measure', 'unit_cost',
            'notes', 'is_critical'
        ]
        
        widgets = {
            'raw_material': forms.Select(attrs={
                'class': 'form-select',
                'data-live-search': 'true'
            }),
            'required_quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'unit_of_measure': forms.Select(attrs={
                'class': 'form-select'
            }),
            'unit_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'readonly': True  # سيتم تحديثه تلقائياً
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'ملاحظات حول المادة الخام'
            }),
            'is_critical': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        
        labels = {
            'raw_material': 'المادة الخام',
            'required_quantity': 'الكمية المطلوبة',
            'unit_of_measure': 'وحدة القياس',
            'unit_cost': 'تكلفة الوحدة',
            'notes': 'ملاحظات',
            'is_critical': 'مادة حرجة',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تحديد المواد الخام المتاحة
        self.fields['raw_material'].queryset = ProductDefinition.objects.filter(
            is_active=True, product_type='raw_material'
        ).order_by('name')
        
        self.fields['unit_of_measure'].queryset = UnitDefinition.objects.filter(is_active=True).order_by('name')
        
        # جعل الحقول المطلوبة واضحة
        required_fields = ['raw_material', 'required_quantity', 'unit_of_measure']
        for field_name in required_fields:
            self.fields[field_name].required = True
    
    def clean(self):
        cleaned_data = super().clean()
        required_quantity = cleaned_data.get('required_quantity')
        raw_material = cleaned_data.get('raw_material')
        
        # التحقق من أن الكمية أكبر من صفر
        if required_quantity and required_quantity <= 0:
            raise ValidationError('الكمية المطلوبة يجب أن تكون أكبر من صفر')
        
        # تحديث تكلفة الوحدة من تعريف المنتج
        if raw_material and raw_material.cost_price:
            cleaned_data['unit_cost'] = raw_material.cost_price
        
        return cleaned_data


# إنشاء Formset للمواد الخام
ManufacturingOrderRawMaterialFormSet = inlineformset_factory(
    ManufacturingOrder,
    ManufacturingOrderRawMaterial,
    form=ManufacturingOrderRawMaterialForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)
